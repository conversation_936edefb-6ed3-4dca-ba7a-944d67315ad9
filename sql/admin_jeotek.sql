-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: May 27, 2025 at 08:22 AM
-- Server version: 10.6.11-MariaDB
-- PHP Version: 8.0.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `admin_jeotek`
--

-- --------------------------------------------------------

--
-- Table structure for table `addons`
--

CREATE TABLE `addons` (
  `id` int(11) NOT NULL,
  `adi` varchar(100) NOT NULL,
  `tekil_adi` varchar(100) NOT NULL,
  `cogul_adi` varchar(100) NOT NULL,
  `durum` enum('aktif','pasif') DEFAULT NULL,
  `anahtar` varchar(30) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `category`
--

CREATE TABLE `category` (
  `category_id` int(11) NOT NULL,
  `isletme_id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL,
  `owner_id` int(11) NOT NULL,
  `status` enum('Aktif','Pasif','Gizli') NOT NULL,
  `position` int(11) NOT NULL,
  `category_seo_url` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `object_type` varchar(50) NOT NULL,
  `lang` varchar(10) NOT NULL DEFAULT 'tr',
  `rapor_id` int(11) NOT NULL,
  `is_delete` char(1) NOT NULL DEFAULT '0'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `documents`
--

CREATE TABLE `documents` (
  `id` int(11) NOT NULL,
  `isletme_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `title` varchar(150) NOT NULL,
  `relation` varchar(50) NOT NULL,
  `relation_id` int(11) NOT NULL,
  `image_url_target` varchar(50) NOT NULL,
  `c_date` int(11) NOT NULL,
  `action_owner_id` int(11) NOT NULL,
  `position` float NOT NULL,
  `file_size` int(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `il`
--

CREATE TABLE `il` (
  `id` tinyint(4) NOT NULL DEFAULT 0,
  `ad` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ilce`
--

CREATE TABLE `ilce` (
  `id` int(4) NOT NULL,
  `il_id` tinyint(4) NOT NULL,
  `ad` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `laboratuvar`
--

CREATE TABLE `laboratuvar` (
  `id` int(11) NOT NULL,
  `isletme_id` int(11) NOT NULL,
  `rapor_id` int(11) NOT NULL,
  `tur` varchar(10) NOT NULL,
  `kayit_turu` enum('excel','manuel') NOT NULL DEFAULT 'excel',
  `sondaj_no` varchar(20) NOT NULL,
  `numune_no` varchar(20) NOT NULL,
  `derinlik` varchar(20) NOT NULL,
  `su_muhtevasi` varchar(20) NOT NULL,
  `tane_buyuklugu_10` varchar(20) NOT NULL,
  `tane_buyuklugu_200` varchar(20) NOT NULL,
  `likit_limit` varchar(20) NOT NULL,
  `plastik_limit_pl` varchar(20) NOT NULL,
  `plastik_limit_pi` varchar(20) NOT NULL,
  `zemin_sinifi` varchar(20) NOT NULL,
  `kivam_indeksi` varchar(20) NOT NULL,
  `karot_hacim_agirlik` varchar(20) NOT NULL,
  `nokta_yuku_mpa` varchar(20) NOT NULL,
  `nokta_yuku_kg` varchar(20) NOT NULL,
  `c_date` int(11) NOT NULL,
  `action_owner_id` int(11) NOT NULL,
  `u_date` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `log`
--

CREATE TABLE `log` (
  `id` int(11) NOT NULL,
  `isletme_id` int(11) NOT NULL,
  `rapor_id` int(11) NOT NULL,
  `sk_id` int(11) NOT NULL,
  `derinlik_baslangic` varchar(5) NOT NULL,
  `derinlik_bitis` varchar(5) NOT NULL,
  `ornek_turu` enum('karot','spt','ud') NOT NULL,
  `tanim` varchar(10) NOT NULL,
  `0_15` varchar(5) NOT NULL,
  `15_30` varchar(5) NOT NULL,
  `30_45` varchar(5) NOT NULL,
  `n` varchar(5) NOT NULL,
  `elastisite` varchar(15) NOT NULL,
  `limit_basinc` varchar(15) NOT NULL,
  `tcr` varchar(5) NOT NULL,
  `scr` varchar(5) NOT NULL,
  `rqd` varchar(5) NOT NULL,
  `ayrisma_derecesi` varchar(10) NOT NULL,
  `catlak_sikligi` varchar(15) NOT NULL,
  `dayanim` varchar(5) NOT NULL,
  `c_date` int(11) NOT NULL,
  `u_date` int(11) NOT NULL,
  `action_owner_id` int(11) NOT NULL,
  `position` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `options`
--

CREATE TABLE `options` (
  `option_id` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(100) NOT NULL DEFAULT '0',
  `option_desc` mediumtext NOT NULL,
  `option_name` varchar(64) NOT NULL DEFAULT '',
  `option_value` longtext NOT NULL,
  `input_type` char(1) DEFAULT 'I',
  `autoload` varchar(20) NOT NULL DEFAULT 'yes',
  `lang` varchar(10) DEFAULT 'tr'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `raporlar`
--

CREATE TABLE `raporlar` (
  `id` int(11) NOT NULL,
  `isletme_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `is_delete` char(1) NOT NULL DEFAULT '0',
  `proje_adi` varchar(200) NOT NULL,
  `il` varchar(50) NOT NULL,
  `il_id` int(11) NOT NULL,
  `ilce` varchar(50) NOT NULL,
  `mahalle` varchar(50) NOT NULL,
  `pafta` varchar(20) NOT NULL,
  `ada` varchar(20) NOT NULL,
  `parsel` varchar(5) NOT NULL,
  `latitude` varchar(100) NOT NULL,
  `longitude` varchar(100) NOT NULL,
  `c_date` int(11) NOT NULL,
  `u_date` int(11) NOT NULL,
  `action_owner_id` int(11) NOT NULL,
  `durum` enum('bitti','devam-ediyor') NOT NULL DEFAULT 'devam-ediyor',
  `sondaj_kroki` varchar(100) NOT NULL,
  `pdf_file` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `relationships`
--

CREATE TABLE `relationships` (
  `object_id` mediumint(8) NOT NULL,
  `relationships_id` mediumint(8) NOT NULL,
  `object_type` varchar(50) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sklar`
--

CREATE TABLE `sklar` (
  `id` int(11) NOT NULL,
  `isletme_id` int(11) NOT NULL,
  `rapor_id` int(11) NOT NULL,
  `sorumlu_muh` int(11) NOT NULL,
  `sondaj_kotu` varchar(10) NOT NULL,
  `sondaj_derinligi` varchar(5) NOT NULL,
  `baslama_tarihi` int(11) NOT NULL,
  `bitis_tarihi` int(11) NOT NULL,
  `makine_tipi` varchar(50) NOT NULL,
  `sahmerdan_tipi` varchar(50) NOT NULL,
  `delgi_capi` varchar(50) NOT NULL,
  `adi` varchar(50) NOT NULL,
  `c_date` int(11) NOT NULL,
  `u_date` int(11) NOT NULL,
  `action_owner_id` int(11) NOT NULL,
  `position` float NOT NULL,
  `latitude` varchar(100) NOT NULL,
  `longitude` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sondaj_kabul`
--

CREATE TABLE `sondaj_kabul` (
  `id` int(11) NOT NULL,
  `rapor_id` int(11) NOT NULL,
  `part` int(11) NOT NULL,
  `content` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` mediumint(8) NOT NULL,
  `isletme_id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL,
  `status` char(1) NOT NULL DEFAULT '1',
  `user_type` enum('Employee','Provider','Admin','Customer') DEFAULT NULL,
  `c_date` int(11) NOT NULL,
  `u_date` int(11) NOT NULL,
  `action_owner_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `lastname` varchar(100) NOT NULL,
  `gender` enum('Erkek','Kadın','Diğer') NOT NULL,
  `email` varchar(50) NOT NULL,
  `pass` varchar(16) NOT NULL,
  `avatar` varchar(100) NOT NULL,
  `gsm` varchar(20) NOT NULL,
  `is_delete` char(1) NOT NULL DEFAULT '0',
  `last_login` int(11) NOT NULL,
  `firma_adi` varchar(100) NOT NULL,
  `country_code` varchar(10) NOT NULL DEFAULT '90',
  `belge_no` varchar(100) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_options`
--

CREATE TABLE `user_options` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `firma_adi` varchar(100) NOT NULL,
  `logo_light` varchar(50) NOT NULL,
  `logo_dark` varchar(50) NOT NULL,
  `favicon` varchar(50) NOT NULL,
  `adress` varchar(255) NOT NULL,
  `telefon` varchar(30) NOT NULL,
  `email` varchar(30) NOT NULL,
  `c_date` int(11) NOT NULL,
  `u_date` int(11) NOT NULL,
  `action_owner_id` int(11) NOT NULL,
  `form_submit_option` varchar(30) NOT NULL DEFAULT 'Kaydet ve Kal',
  `storage` int(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `wordtohtml`
--

CREATE TABLE `wordtohtml` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `content` longtext DEFAULT NULL,
  `page_number` int(11) DEFAULT NULL,
  `original_file` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_delete` tinyint(1) NOT NULL DEFAULT 0,
  `heading1` varchar(255) DEFAULT NULL,
  `heading2` varchar(255) DEFAULT NULL,
  `subtitle` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `yeralti_sulari`
--

CREATE TABLE `yeralti_sulari` (
  `id` int(11) NOT NULL,
  `sk_id` int(11) NOT NULL,
  `derinlik` varchar(10) NOT NULL,
  `tarih` int(11) NOT NULL,
  `aciklama` text NOT NULL,
  `rapor_id` int(11) NOT NULL,
  `isletme_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `zemin_tanimi`
--

CREATE TABLE `zemin_tanimi` (
  `id` int(11) NOT NULL,
  `isletme_id` int(11) NOT NULL,
  `rapor_id` int(11) NOT NULL,
  `sk` varchar(20) NOT NULL,
  `zemin_rengi` varchar(50) NOT NULL,
  `baslangic` varchar(5) NOT NULL,
  `bitis` varchar(5) NOT NULL,
  `tanim` varchar(255) NOT NULL,
  `position` float NOT NULL,
  `type` enum('spt','karot') NOT NULL,
  `action_owner_id` int(11) NOT NULL,
  `ekleyen` enum('otomatik','manuel') NOT NULL,
  `numune_no` varchar(10) NOT NULL,
  `zemin_sinifi` varchar(20) NOT NULL,
  `icerik` enum('lab','merge') NOT NULL DEFAULT 'lab'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `zemin_tanimi_for_log`
--

CREATE TABLE `zemin_tanimi_for_log` (
  `id` int(11) NOT NULL,
  `rapor_id` int(11) NOT NULL,
  `sk` varchar(20) NOT NULL,
  `render_col` varchar(10) NOT NULL,
  `zemin_sinifi` varchar(10) NOT NULL,
  `baslangic` varchar(5) NOT NULL,
  `bitis` varchar(5) NOT NULL,
  `zemin_rengi` varchar(50) NOT NULL,
  `tanim` varchar(100) NOT NULL,
  `tanim_aciklama` varchar(150) NOT NULL,
  `round_bitis` varchar(5) NOT NULL,
  `rowspan_row` varchar(10) NOT NULL,
  `rowspan` char(1) NOT NULL DEFAULT '0',
  `baslangic_round` varchar(10) NOT NULL,
  `derinlik_aciklama` varchar(100) NOT NULL,
  `position_top` varchar(100) NOT NULL,
  `position_left` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `addons`
--
ALTER TABLE `addons`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `category`
--
ALTER TABLE `category`
  ADD PRIMARY KEY (`category_id`),
  ADD KEY `parent_id` (`parent_id`,`status`),
  ADD KEY `category_seo_url` (`category_seo_url`(250)),
  ADD KEY `isletme_id` (`isletme_id`);

--
-- Indexes for table `documents`
--
ALTER TABLE `documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `isletme_id` (`isletme_id`);

--
-- Indexes for table `il`
--
ALTER TABLE `il`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `il` ADD FULLTEXT KEY `ad` (`ad`);

--
-- Indexes for table `ilce`
--
ALTER TABLE `ilce`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `laboratuvar`
--
ALTER TABLE `laboratuvar`
  ADD PRIMARY KEY (`id`),
  ADD KEY `isletme_id` (`isletme_id`),
  ADD KEY `rapor_id` (`rapor_id`);

--
-- Indexes for table `log`
--
ALTER TABLE `log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `isletme_id` (`isletme_id`),
  ADD KEY `rapor_id` (`rapor_id`);

--
-- Indexes for table `options`
--
ALTER TABLE `options`
  ADD PRIMARY KEY (`option_id`);

--
-- Indexes for table `raporlar`
--
ALTER TABLE `raporlar`
  ADD PRIMARY KEY (`id`),
  ADD KEY `isletme_id` (`isletme_id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `relationships`
--
ALTER TABLE `relationships`
  ADD KEY `relationships_id` (`relationships_id`),
  ADD KEY `object_type` (`object_type`);

--
-- Indexes for table `sklar`
--
ALTER TABLE `sklar`
  ADD PRIMARY KEY (`id`),
  ADD KEY `isletme_id` (`isletme_id`),
  ADD KEY `rapor_id` (`rapor_id`);

--
-- Indexes for table `sondaj_kabul`
--
ALTER TABLE `sondaj_kabul`
  ADD PRIMARY KEY (`id`),
  ADD KEY `rapor_id` (`rapor_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD KEY `status` (`status`,`email`,`pass`),
  ADD KEY `user_type` (`user_type`),
  ADD KEY `isletme_id` (`isletme_id`);

--
-- Indexes for table `user_options`
--
ALTER TABLE `user_options`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `wordtohtml`
--
ALTER TABLE `wordtohtml`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `yeralti_sulari`
--
ALTER TABLE `yeralti_sulari`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sk_id` (`sk_id`);

--
-- Indexes for table `zemin_tanimi`
--
ALTER TABLE `zemin_tanimi`
  ADD PRIMARY KEY (`id`),
  ADD KEY `rapor_id` (`rapor_id`),
  ADD KEY `isletme_id` (`isletme_id`);

--
-- Indexes for table `zemin_tanimi_for_log`
--
ALTER TABLE `zemin_tanimi_for_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `rapor_id` (`rapor_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `addons`
--
ALTER TABLE `addons`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `category`
--
ALTER TABLE `category`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `documents`
--
ALTER TABLE `documents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ilce`
--
ALTER TABLE `ilce`
  MODIFY `id` int(4) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `laboratuvar`
--
ALTER TABLE `laboratuvar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `log`
--
ALTER TABLE `log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `options`
--
ALTER TABLE `options`
  MODIFY `option_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `raporlar`
--
ALTER TABLE `raporlar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sklar`
--
ALTER TABLE `sklar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sondaj_kabul`
--
ALTER TABLE `sondaj_kabul`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` mediumint(8) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_options`
--
ALTER TABLE `user_options`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `wordtohtml`
--
ALTER TABLE `wordtohtml`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `yeralti_sulari`
--
ALTER TABLE `yeralti_sulari`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `zemin_tanimi`
--
ALTER TABLE `zemin_tanimi`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `zemin_tanimi_for_log`
--
ALTER TABLE `zemin_tanimi_for_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
