<?php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    if ($_FILES) {

        foreach ($_FILES as $k => $v) {
            if ($_FILES[$k]['name']) {
                $images_dir = images_url($target);
                $data[$k] = fn_file_upload($_FILES[$k], $images_dir);
            }
        }
    }
    $data['full_name'] = $data['name'] . ' ' . $data['lastname'];
    if ($mode == 'add') {
        $id = fn_insert_array('users', $data);
        fn_set_notice("Bilgiler eklendi.");
    }

    if ($mode == 'update') {
        fn_update_array('users', $data, "user_id=$id limit 1");
        fn_set_notice("Bilgiler Güncellendi.");
    }
    if (USER_LOGIN == $id)
        fn_login_after($id);
    fn_redirect_submit("?do=$target/manage");
}


if ($mode == 'update' && $id) {
    $data = get_row("users", "user_id=$id");
    $data->id = $data->user_id;
}

?>
<form class="form valid_form" method="POST" enctype="multipart/form-data">
    <input type="hidden" name="mode" value="<?php echo $mode ?>">
    <input type="hidden" name="data[user_type]" value="Employee">
    <input type="hidden" name="data[parent_id]" value="0">
    <?php if (isset($data) && $data->id) : ?>
        <input type="hidden" name="id" value="<?php echo $data->id ?>">
    <?php endif ?>
    <div class="card card-custom">
        <?php include  COMMON_DIR . '/card/title.php'; ?>
        <div class="card-body">
            <?php
            form_group_avatar('Profil Resmi', 'avatar');
            form_group_input_common('Adı', 'name', 'required');
            form_group_input_common('Soyadı', 'lastname', 'required');
            form_group_input_common('Belge No.', 'belge_no', 'required');
            ?>
        </div>
        <?php include  COMMON_DIR . '/card/footer.php';  ?>
    </div>
</form>