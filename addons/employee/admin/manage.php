<?php

$data = $db->get_results("SELECT * FROM users WHERE is_delete=0 AND (user_type='Employee') AND isletme_id=" . ISLETME_ID);
?>
<div class="card card-flush py-4 ">
    <?php include COMMON_DIR . '/layouts/_manager_header.php'; ?>
    <div class="card-body  ">
        <div class="table-responsive">
            <table class="table" id="custom_data_table">
                <thead>
                    <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                        <th>#</th>
                        <th><?php echo _('Adı'); ?></th>
                        <th><?php echo _('Soyadı'); ?></th>
                        <th><?php echo _('Belge No'); ?></th>
                        <th></th>
                    </tr>
                </thead>
                <tbody class="fw-semibold text-gray-600">
                    <?php if ($data) : ?>
                        <?php foreach ($data as $key => $v) :
                            $v->id = $v->user_id; ?>
                            <tr id="row-<?php echo $v->user_id ?>">
                                <td><?php echo $v->user_id ?></td>
                                <td><?php echo $v->name; ?></td>
                                <td><?php echo $v->lastname; ?></td>
                                <td><?php echo $v->belge_no; ?></td>
                                <td nowrap="nowrap" class="text-end">
                                    <?php table_action_row($v); ?>
                                </td>
                            </tr>
                        <?php endforeach ?>
                    <?php endif ?>
                </tbody>
            </table>
        </div>
    </div>
</div>