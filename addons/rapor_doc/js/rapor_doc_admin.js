// CKEditor Document entegrasyonu - DecoupledEditor versiyonu
$(document).ready(function () {
    // Global uploadEditorUrl değişkenini kontrol et
    if (typeof uploadEditorUrl === 'undefined') {
        // Eğer tanımlı değilse varsayılan bir değer ata
        window.uploadEditorUrl = '/index.php?do=upload/ckeditor';
    }

    // Sayfa yüklenirken önceki editörleri temizle
    $('.document-editor__toolbar').remove();
    $('.document-editor').removeClass('document-editor');

    // EVOCkeditor fonksiyonunu geçici olarak devre dışı bırak
    if (window.EVOCkeditor && window.EVOCkeditor.init) {
        var originalInit = window.EVOCkeditor.init;
        window.EVOCkeditor.init = function() {
            console.log('EVOCkeditor.init geçici olarak devre dışı bırakıldı');
        };

        // Sayfa kapanırken orijinal fonksiyonu geri yükle
        $(window).on('unload', function() {
            window.EVOCkeditor.init = originalInit;
        });
    }

    // Editör elementi kontrolü - Benzersiz sınıf ile
    var editorElement = document.querySelector('.rapor-doc-editor-unique');
    if (!editorElement) {
        console.error('Rapor Doc Editor elementi bulunamadı!');
        return;
    }

    // Editörün parent elementini al
    var editorContainer = editorElement.parentNode;
    if (!editorContainer) {
        console.error('Rapor Doc Editor parent elementi bulunamadı!');
        return;
    }

    // Araç çubuğu için container oluştur
    var toolbarContainer = document.createElement('div');
    toolbarContainer.id = 'toolbar-' + editorElement.id;
    toolbarContainer.classList.add('document-editor__toolbar');

    // Araç çubuğunu editörün üstüne yerleştir
    editorContainer.insertBefore(toolbarContainer, editorElement);

    // Editör container'a CSS sınıfı ekle
    editorContainer.classList.add('document-editor');

    // DecoupledEditor'u oluştur
    DecoupledEditor
        .create(editorElement, {
            // CKEditor Document konfigürasyonu
            language: 'tr',
            toolbar: {
                items: [
                    'heading',
                    '|',
                    'bold',
                    'italic',
                    'link',
                    'bulletedList',
                    'numberedList',
                    '|',
                    'outdent',
                    'indent',
                    '|',
                    'imageUpload',
                    'blockQuote',
                    'insertTable',
                    'undo',
                    'redo',
                    '|',
                    'alignment',
                    'fontColor',
                    'fontBackgroundColor',
                    'fontSize',
                    'fontFamily',
                    '|',
                    'horizontalLine',
                    'pageBreak',
                    'specialCharacters',
                    'removeFormat',
                    'sourceEditing'
                ]
            },
            ckfinder: {
                // Eğer uploadEditorUrl tanımlı değilse varsayılan bir URL kullan
                uploadUrl: (typeof uploadEditorUrl !== 'undefined') ? uploadEditorUrl : '/index.php?do=rapor_doc/uploadimagefromeditor'
            },
            image: {
                toolbar: [
                    'imageTextAlternative',
                    'toggleImageCaption',
                    'imageStyle:inline',
                    'imageStyle:block',
                    'imageStyle:side'
                ]
            },
            table: {
                contentToolbar: [
                    'tableColumn',
                    'tableRow',
                    'mergeTableCells',
                    'tableCellProperties',
                    'tableProperties'
                ]
            },
            htmlSupport: {
                allow: [
                    {
                        name: /^.*$/,
                        styles: true,
                        attributes: true,
                        classes: true
                    }
                ]
            },
            // Tüm HTML içeriğine izin ver
            allowedContent: true,
            mediaEmbed: {
                previewsInData: true
            }
        })
        .then(function(editor) {
            // Araç çubuğunu oluşturulan div'e ekle
            toolbarContainer.appendChild(editor.ui.view.toolbar.element);

            // Form gönderilmeden önce içeriği gizli textarea'ya aktar
            var hiddenTextarea = document.getElementById('description_hidden');

            if (hiddenTextarea) {
                // Editör içeriği değiştiğinde gizli textarea'yı güncelle
                editor.model.document.on('change:data', function() {
                    // getData() doğrudan string döndürür, Promise döndürmez
                    var data = editor.getData();
                    hiddenTextarea.value = data;
                });

                // Form gönderilmeden önce içeriği güncelle
                var form = editorElement.closest('form');
                if (form) {
                    form.addEventListener('submit', function() {
                        // getData() doğrudan string döndürür, Promise döndürmez
                        var data = editor.getData();
                        hiddenTextarea.value = data;
                    });
                }
            }

            console.log('Rapor Doc Editor (DecoupledEditor) başlatıldı:', editor);
        })
        .catch(function(error) {
            console.error('Rapor Doc Editor başlatılırken hata oluştu:', error);
        });
});
