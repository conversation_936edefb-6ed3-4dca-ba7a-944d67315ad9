<?php

/**
 * CKEditor için dosya yükleme işlemini gerçekleştiren dosya
 */

// Yetki kontrolü
if (!USER_LOGIN) {
    $response = array(
        'uploaded' => 0,
        'error' => array(
            'message' => 'Yetkiniz yok!'
        )
    );
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Yükleme klasörü
$upload_dir = ADDONS_DIR . '/rapor_doc/uploads/';

// Klasör yoksa oluştur
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// Dosya yükleme kontrolü
if (isset($_FILES['upload']) && !empty($_FILES['upload']['name'])) {
    $file = $_FILES['upload'];

    // Hata kontrolü
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $response = array(
            'uploaded' => 0,
            'error' => array(
                'message' => 'Dosya yüklenirken bir hata oluştu: ' . $file['error']
            )
        );
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    // Dosya türü kontrolü
    $allowed_types = array('image/jpeg', 'image/png', 'image/gif', 'image/webp');
    if (!in_array($file['type'], $allowed_types)) {
        $response = array(
            'uploaded' => 0,
            'error' => array(
                'message' => 'Sadece JPEG, PNG, GIF ve WEBP formatları desteklenmektedir.'
            )
        );
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    // Dosya boyutu kontrolü (5MB)
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        $response = array(
            'uploaded' => 0,
            'error' => array(
                'message' => 'Dosya boyutu 5MB\'dan büyük olamaz.'
            )
        );
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    // Dosya adını güvenli hale getir
    $file_name = preg_replace('/[^a-zA-Z0-9_.-]/', '', $file['name']);
    $file_name = strtolower($file_name);

    // Benzersiz dosya adı oluştur
    $file_name = time() . '_' . $file_name;

    // Dosya uzantısını al
    $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);

    // Dosya adını oluştur
    $file_name = md5(uniqid()) . '.' . $file_ext;

    // Dosya yolu
    $file_path = $upload_dir . $file_name;

    // Dosyayı yükle
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // Web URL'sini oluştur
        $web_url = WEB_DIR . '/addons/rapor_doc/uploads/' . $file_name;

        // Başarılı yanıt
        $response = array(
            'uploaded' => 1,
            'fileName' => $file_name,
            'url' => $web_url
        );

        // Veritabanına kaydet
        $data = array(
            'file_name' => $file_name,
            'file_path' => $file_path,
            'file_url' => $web_url,
            'file_type' => $file['type'],
            'file_size' => $file['size'],
            'upload_date' => date('Y-m-d H:i:s'),
            'user_id' => USER_LOGIN,
            'isletme_id' => ISLETME_ID
        );

        // Eğer veritabanına kaydetmek istiyorsanız
        // fn_insert_array('rapor_doc_uploads', $data);

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    } else {
        // Hata yanıtı
        $response = array(
            'uploaded' => 0,
            'error' => array(
                'message' => 'Dosya yüklenirken bir hata oluştu.'
            )
        );
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
} else {
    // Dosya yok
    $response = array(
        'uploaded' => 0,
        'error' => array(
            'message' => 'Dosya bulunamadı.'
        )
    );
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
