<?php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    if ($mode == 'delete') {

        fn_category_delete($id);
        $data = array();
        send_json($data);
    }
}

// CKEditor için dosya yükleme işlemi
if ($mode == 'uploadimagefromeditor') {
    include(ADDONS_DIR . '/' . $target . '/admin/uploadimagefromeditor.php');
    exit;
}

// Word dosyasından kategori içe aktarma
if ($mode == 'import_word_categories') {
    include(ADDONS_DIR . '/' . $target . '/admin/import_word_categories.php');
    exit;
}

include(ADDONS_DIR . '/' . $target . '/admin/' . $mode . '.php');
