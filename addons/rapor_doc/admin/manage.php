<?php

// $tum_cat = get_results('category', "object_type='rapor_doc'");
// foreach ($tum_cat as $k => $v) {
//     $data['position'] = $v->category_id;
//     fn_update_array('category', $data, "category_id=$v->category_id limit 1");
// }
if (@$delete) {

    foreach ($data as $k => $v) {

        fn_category_delete($v);
    }

    fn_set_notice("O", "Seçili Kategoriler silindi.");

    fn_redirect("?do=category/manage");
}
?>
<?php
$categories = fn_get_categories_tree('rapor_doc', 0, 0, 0);

?>
<div class="card card-flush">
    <?php
    $_search_box = false;
    include $s_tema . '/layouts/_manager_header.php'; ?>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm align-middle table-row-dashed  ">
                <thead>
                    <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">

                        <th><?php echo _('Kategori Adı'); ?></th>
                        <th><?php echo _('Durum'); ?></th>
                        <th></th>
                    </tr>
                </thead>
                <tbody class="fw-bold text-gray-600">
                    <?php if ($categories) : ?>
                        <?php foreach ($categories as $k => $v) : ?>
                            <tr id="row-<?php echo $v['category_id']; ?>" class="<?php echo ($id == $v['category_id']) ? 'bg-light' : ''; ?>">

                                <td>
                                    <?php
                                    $style = '';
                                    for ($j = 0; $j < $categories[$k][1]; $j++) {
                                        echo '&nbsp;&nbsp;&nbsp;&nbsp;';
                                        $style = 'fw-semibold ';
                                    }
                                    ?>
                                    <a class="<?php echo $style; ?>" href="?do=rapor_doc/update&id=<?php echo $v['category_id']; ?>"><?php echo $v['category']; ?></a>
                                </td>
                                <td>
                                    <?php echo ($v['status']); ?>
                                </td>
                                <td nowrap="nowrap" class="text-end">
                                    <?php
                                    table_action_row($v['category_id']);
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" class="text-center text-muted py-3 mt-2"><?php kayit_bulunamadi(); ?></td>
                        </tr>
                    <?php endif ?>
                </tbody>
            </table>
        </div>
    </div>
</div>