<?php

/**
 * Word dosyasındaki başlık ve alt başlıkları sisteme ekleyen betik
 */


// Yetki kontrolü
if (!USER_LOGIN) {
    header('Location: ' . WEB_DIR . '/index.php?do=login');
    exit;
}

// Kategori ekleme fonksiyonu
function add_category($category, $parent_id = 0, $description = '', $object_type = 'rapor_doc', $status = 'Aktif')
{
    global $db;

    // Hata ayıklama için log
    echo "<div style='color:blue;'>Kategori ekleniyor: $category (Parent ID: $parent_id)</div>";

    // SEO URL oluştur
    $category_seo_url = fn_MakeSeo($category);

    // Kategori zaten var mı kontrol et
    $sql = "SELECT category_id FROM category WHERE category = '" . $db->escape($category) . "' AND object_type = '" . $db->escape($object_type) . "' AND parent_id = " . intval($parent_id) . " AND isletme_id = " . ISLETME_ID;
    $result = $db->get_row($sql);

    if ($result && isset($result->category_id)) {
        echo "Kategori zaten mevcut: $category (ID: {$result->category_id})<br>";
        return $result->category_id;
    }

    // Kategori ekle - fn_insert_array kullanarak
    $data = array(
        'parent_id' => intval($parent_id),
        'category' => $category,
        'description' => $description,
        'object_type' => $object_type,
        'status' => $status,
        'isletme_id' => ISLETME_ID,
        'owner_id' => USER_LOGIN,
        'category_seo_url' => $category_seo_url
    );

    echo "<div style='color:purple;'>Data: " . print_r($data, true) . "</div>";

    // fn_insert_array fonksiyonunu kullanarak kategori ekle
    $category_id = fn_insert_array('category', $data);

    echo "<div style='color:red;'>category_id: $category_id</div>";

    if ($category_id) {
        echo "Kategori eklendi: $category (ID: $category_id)<br>";
        return $category_id;
    } else {
        echo "<div class='alert alert-danger'>Kategori eklenirken bir hata oluştu: " . $db->last_error . "</div>";
        return 0;
    }
}

// Ana kategorileri ve alt kategorileri ekleyelim
$categories = [
    // 1. GİRİŞ
    [
        'name' => 'GİRİŞ',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Giriş Bölümü',
        'children' => [
            // 1.1. Etüdün Amacı ve Kapsamı
            [
                'name' => 'Etüdün Amacı ve Kapsamı',
                'description' => 'Etüdün amacı ve kapsamı hakkında bilgiler',
                'children' => []
            ],
            // 1.2. İnceleme Alanının Tanıtılması
            [
                'name' => 'İnceleme Alanının Tanıtılması',
                'description' => 'İnceleme alanının tanıtılması hakkında bilgiler',
                'children' => [
                    // 1.2.1. Jeomorfolojik ve Çevresel Bilgiler
                    [
                        'name' => 'Jeomorfolojik ve Çevresel Bilgiler',
                        'description' => 'Jeomorfolojik ve çevresel bilgiler hakkında detaylar',
                        'children' => []
                    ],
                    // 1.2.2. İmar Planı Durumu
                    [
                        'name' => 'İmar Planı Durumu',
                        'description' => 'İmar planı durumu hakkında bilgiler',
                        'children' => []
                    ],
                    // 1.2.3. İmar Adası İle İlgili Bilgiler
                    [
                        'name' => 'İmar Adası İle İlgili Bilgiler',
                        'description' => 'İmar adası ile ilgili bilgiler',
                        'children' => []
                    ],
                    // 1.2.4. İklim Bilgileri
                    [
                        'name' => 'İklim Bilgileri',
                        'description' => 'İklim bilgileri hakkında detaylar',
                        'children' => []
                    ],
                    // 1.2.5. Doğal Afet Tehlikeleri
                    [
                        'name' => 'Doğal Afet Tehlikeleri',
                        'description' => 'Doğal afet tehlikeleri hakkında bilgiler',
                        'children' => []
                    ],
                    // 1.2.6. Yapı Hakkında Bilgiler
                    [
                        'name' => 'Yapı Hakkında Bilgiler',
                        'description' => 'Yapı hakkında bilgiler',
                        'children' => []
                    ]
                ]
            ]
        ]
    ],
    // 2. JEOLOJİ
    [
        'name' => 'JEOLOJİ',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Jeoloji Bölümü',
        'children' => [
            // 2.1. Bölgesel Jeoloji
            [
                'name' => 'Bölgesel Jeoloji',
                'description' => 'Bölgesel jeoloji hakkında bilgiler',
                'children' => []
            ],
            // 2.2. Yapısal Jeoloji ve Aktif Tektonik
            [
                'name' => 'Yapısal Jeoloji ve Aktif Tektonik',
                'description' => 'Yapısal jeoloji ve aktif tektonik hakkında bilgiler',
                'children' => []
            ]
        ]
    ],
    // 3. ARAZİ ÇALIŞMALARI
    [
        'name' => 'ARAZİ ÇALIŞMALARI',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Arazi Çalışmaları Bölümü',
        'children' => [
            // 3.1. Jeofizik çalışmalar
            [
                'name' => 'Jeofizik çalışmalar',
                'description' => 'Jeofizik çalışmalar hakkında bilgiler',
                'children' => []
            ]
        ]
    ],
    // 4. Araştırma Çukurları, Sondajlar ve Arazi Deneyleri
    [
        'name' => 'Araştırma, Sondaj ve Deneyler',
        'description' => 'Araştırma Çukurları, Sondajlar ve Arazi Deneyleri',
        'children' => [
            // 4.2. Araştırma Çukurları
            [
                'name' => 'Araştırma Çukurları',
                'description' => 'Araştırma çukurları hakkında bilgiler',
                'children' => []
            ],
            // 4.3. Sondajlar
            [
                'name' => 'Sondajlar',
                'description' => 'Sondajlar hakkında bilgiler',
                'children' => []
            ],
            // 4.4. Arazi Deneyleri
            [
                'name' => 'Arazi Deneyleri',
                'description' => 'Arazi deneyleri hakkında bilgiler',
                'children' => [
                    // 4.4.1. Standart Penetrasyon Deneyi (SPT)
                    [
                        'name' => 'Standart Penetrasyon Deneyi (SPT)',
                        'description' => 'Standart Penetrasyon Deneyi (SPT) hakkında bilgiler',
                        'children' => []
                    ],
                    // 4.4.2. Presiyometre Deneyi
                    [
                        'name' => 'Presiyometre Deneyi',
                        'description' => 'Presiyometre Deneyi hakkında bilgiler',
                        'children' => []
                    ]
                ]
            ]
        ]
    ],
    // 5. HİDROJEOLOJİ
    [
        'name' => 'HİDROJEOLOJİ',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Hidrojeoloji Bölümü',
        'children' => []
    ],
    // 6. LABORATUVAR DENEYLERİ
    [
        'name' => 'LABORATUVAR DENEYLERİ',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Laboratuvar Deneyleri Bölümü',
        'children' => [
            // 6.1. Zeminlerin İndeks / Fiziksel Özelliklerinin Belirlenmesi
            [
                'name' => 'Zeminlerin İndeks / Fiziksel Özelliklerinin Belirlenmesi',
                'description' => 'Zeminlerin indeks / fiziksel özelliklerinin belirlenmesi hakkında bilgiler',
                'children' => [
                    // 6.1.1. Su İçeriği (Su Muhtevası) Analizi
                    [
                        'name' => 'Su İçeriği (Su Muhtevası) Analizi',
                        'description' => 'Su içeriği (su muhtevası) analizi hakkında bilgiler',
                        'children' => []
                    ],
                    // 6.1.2. Kıvam Limitleri Analizi
                    [
                        'name' => 'Kıvam Limitleri Analizi',
                        'description' => 'Kıvam limitleri analizi hakkında bilgiler',
                        'children' => []
                    ]
                ]
            ]
        ]
    ],
    // 7. İNCELEME ALANI MÜHENDİSLİK JEOLOJİSİ
    [
        'name' => 'İNCELEME ALANI MÜHENDİSLİK JEOLOJİSİ',
        'description' => 'Zemin ve Temel Etüd Veri Raporu İnceleme Alanı Mühendislik Jeolojisi Bölümü',
        'children' => []
    ],
    // 8. JEOLOJİK KESİT
    [
        'name' => 'JEOLOJİK KESİT',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Jeolojik Kesit Bölümü',
        'children' => []
    ],
    // 9. SONUÇ VE ÖNERİLER
    [
        'name' => 'SONUÇ VE ÖNERİLER',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Sonuç ve Öneriler Bölümü',
        'children' => []
    ],
    // 10. YARARLANILAN KAYNAKLAR
    [
        'name' => 'YARARLANILAN KAYNAKLAR',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Yararlanılan Kaynaklar Bölümü',
        'children' => []
    ],
    // 11. EKLER
    [
        'name' => 'EKLER',
        'description' => 'Zemin ve Temel Etüd Veri Raporu Ekler Bölümü',
        'children' => []
    ]
];

// Kategorileri ekleyen fonksiyon
function process_categories($categories, $parent_id = 0)
{
    echo "<div style='color:green;'>process_categories çağrıldı (Parent ID: $parent_id)</div>";

    foreach ($categories as $category) {
        $id = add_category($category['name'], $parent_id, $category['description']);

        echo "<div style='color:green;'>Kategori eklendi: {$category['name']} (ID: $id, Parent ID: $parent_id)</div>";

        if (!empty($category['children'])) {
            echo "<div style='color:green;'>Alt kategoriler işleniyor: {$category['name']} (ID: $id)</div>";
            process_categories($category['children'], $id);
        }
    }
}

// Ana kategorileri ekleyelim
process_categories($categories);

echo "<div class='alert alert-success'>Tüm kategoriler başarıyla eklendi!</div>";
