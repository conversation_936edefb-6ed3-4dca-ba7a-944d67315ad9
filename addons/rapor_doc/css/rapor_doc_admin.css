/* CKEditor DecoupledEditor için stil tanımlamaları */
.document-editor {
    border: 1px solid var(--kt-border-color);
    border-radius: 0.475rem;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 1rem;
}

.document-editor__toolbar {
    background-color: var(--kt-body-bg);
    border-bottom: 1px solid var(--kt-border-color);
    padding: 0.5rem;
    z-index: 1;
}

/* Editör içeriği */
.rapor-doc-editor {
    min-height: 300px;
    padding: 1rem;
    background-color: var(--kt-body-bg);
    border: 1px solid var(--kt-border-color);
    border-top: none;
    border-radius: 0 0 0.475rem 0.475rem;
}

/* Editör içeriği odaklandığında */
.rapor-doc-editor:focus {
    outline: none;
    border-color: var(--kt-primary);
}

/* Araç çubuğu stilleri */
.document-editor .ck-toolbar {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* Araç çubuğu butonları */
.document-editor .ck-toolbar__items {
    flex-wrap: wrap;
}

/* Sayfa genişliği */
.document-editor .ck-content {
    width: 100%;
    min-height: 300px;
}

/* CKEditor içeriği */
.ck-editor__editable {
    min-height: 300px;
    padding: 1rem;
    background-color: var(--kt-body-bg);
    border: none !important;
    box-shadow: none !important;
}

/* CKEditor odaklandığında */
.ck-focused {
    border: none !important;
    box-shadow: none !important;
}

/* Mobil uyumluluk */
@media (max-width: 768px) {
    .document-editor .ck-toolbar__items {
        flex-wrap: wrap;
    }
}
