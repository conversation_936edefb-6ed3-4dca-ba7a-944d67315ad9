<?php

/**
 * Özel belirteçleri işleyerek ilgili fonksiyonları çağıran fonksiyon
 *
 * @param string $content İşlenecek içerik
 * @param array $data İşleme sırasında kullanılacak ek veriler (opsiyonel)
 * @return string İşlenmiş içerik
 */
function fn_process_special_tags($content, $data = array())
{
    // Belirteç tanımlamaları - her belirteç için çağrılacak fonksiyon
    $tag_handlers = array(
        'table_1' => 'fn_special_tag_table_1',
        'table_2' => 'fn_special_tag_table_2',
        'users_list' => 'fn_special_tag_users_list',
        'products_list' => 'fn_special_tag_products_list',
        'category_list' => 'fn_special_tag_category_list'
    );

    // Belirteçleri bul: {belirtec_adi}
    preg_match_all('/\{([a-zA-Z0-9_]+)\}/', $content, $matches);

    if (empty($matches[1])) {
        return $content; // Belirteç bulunamadı, içeriği olduğu gibi döndür
    }

    // Bulunan her belirteç için işlem yap
    foreach ($matches[1] as $key => $tag_name) {
        $full_tag = $matches[0][$key]; // {belirtec_adi} formatındaki tam belirteç

        // Belirteç için tanımlı bir işleyici fonksiyon var mı?
        if (isset($tag_handlers[$tag_name]) && function_exists($tag_handlers[$tag_name])) {
            // İlgili fonksiyonu çağır ve sonucu al
            // echo $tag_handlers[$tag_name];
            // '<br>';
            $replacement = call_user_func($tag_handlers[$tag_name], $data);

            // Belirteci, fonksiyonun döndürdüğü değerle değiştir
            $content = str_replace($full_tag, $replacement, $content);
        } else {
            // Tanımlı bir işleyici yoksa, belirteci bir uyarı ile değiştir
            $content = str_replace($full_tag, '<span class="text-warning">Tanımsız belirteç: ' . htmlspecialchars($tag_name) . '</span>', $content);
        }
    }

    return $content;
}
function fn_special_tag_table_1($arg)
{
    pe($arg);
    return 'selam';
}
