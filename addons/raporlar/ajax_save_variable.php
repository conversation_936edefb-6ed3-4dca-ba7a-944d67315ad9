<?php
/**
 * AJAX Değişken Değeri Kaydetme Endpoint'i
 */

// Yetki kontrolü
if (!USER_LOGIN) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Yetkiniz yok']);
    exit;
}

// POST verilerini al
$variable_name = isset($_POST['variable_name']) ? trim($_POST['variable_name']) : '';
$variable_value = isset($_POST['variable_value']) ? $_POST['variable_value'] : '';
$rapor_id = isset($_POST['rapor_id']) ? intval($_POST['rapor_id']) : 0;

if (empty($variable_name)) {
    echo json_encode(['success' => false, 'error' => 'Değişken adı gerekli']);
    exit;
}

// Değişken tanımlamaları dosyasını dahil et
require_once ADDONS_DIR . '/raporlar/config/variables.php';
global $rapor_variables;

// Değişkenin düzenlenebilir olup olmadığını kontrol et
if (!isset($rapor_variables[$variable_name]) || !isset($rapor_variables[$variable_name]['editable']) || !$rapor_variables[$variable_name]['editable']) {
    echo json_encode(['success' => false, 'error' => 'Bu değişken düzenlenebilir değil']);
    exit;
}

// Rapor ID kontrolü (eğer rapor özel ise)
if ($rapor_id > 0) {
    $rapor = get_row('raporlar', "id=$rapor_id AND isletme_id=" . ISLETME_ID);
    if (!$rapor) {
        echo json_encode(['success' => false, 'error' => 'Rapor bulunamadı']);
        exit;
    }
}

try {
    // Değişken değerini kaydet
    $result = save_editable_variable_value($variable_name, $variable_value, $rapor_id);
    
    if ($result) {
        // Başarılı kayıt
        $response = [
            'success' => true,
            'message' => 'Değişken başarıyla kaydedildi',
            'variable_name' => $variable_name,
            'rapor_id' => $rapor_id,
            'scope' => $rapor_id > 0 ? 'rapor' : 'global'
        ];
        
        // Yeni değeri de gönder
        $new_value = get_editable_variable_value($variable_name, $rapor_variables[$variable_name]['value'], $rapor_id);
        $response['new_value'] = $new_value;
        
        echo json_encode($response);
    } else {
        echo json_encode(['success' => false, 'error' => 'Değişken kaydedilemedi']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Kaydetme hatası: ' . $e->getMessage()]);
}
?>
