<?php

/**
 * Rapor Değişkenleri Tanımlama Dosyası
 * 
 * Bu dosyada CKEditor içinde kullanılacak değişkenler tanımlanır.
 * Değişkenler {degisken_adi} formatında kullanılır.
 * 
 * Kullanım: {parsel_bilgisi}, {firma_adi}, {rapor_tarihi} vb.
 */

// Güvenlik kontrolü
if (!defined('AREA')) {
    exit('Direct access not allowed');
}

/**
 * Rapor değişkenleri tanımlamaları
 * 
 * Format:
 * 'degisken_adi' => [
 *     'label' => 'Kullanıcıya gösterilecek açıklama',
 *     'description' => 'Değişkenin ne işe yaradığı hakkında detaylı açıklama',
 *     'type' => 'text|sql|function|static', // Değişken tipi
 *     'value' => 'Statik değer veya SQL sorgusu veya fonksiyon adı',
 *     'category' => 'Değişken kategorisi (gruplandırma için)'
 * ]
 */
$rapor_variables = [

    // Parsel ve Arazi Bilgileri
    'parsel_bilgisi' => [
        'label' => 'Parsel Bilgisi',
        'description' => 'Rapor ile ilgili parsel bilgilerini gösterir',
        'type' => 'sql',
        'value' => "SELECT CONCAT(il.ad, ' / ', ilce.ad, ' / ', mahalle, ' / ', ada, ' parsel ', parsel) as parsel_bilgisi FROM raporlar r LEFT JOIN il ON r.il_id = il.id LEFT JOIN ilce ON r.ilce_id = ilce.id WHERE r.id = {rapor_id}",
        'category' => 'Arazi Bilgileri'
    ],

    'rapor_tarihi' => [
        'label' => 'Rapor Tarihi',
        'description' => 'Raporun oluşturulma tarihini gösterir',
        'type' => 'sql',
        'value' => "SELECT DATE_FORMAT(created_date, '%d.%m.%Y') as rapor_tarihi FROM raporlar WHERE id = {rapor_id}",
        'category' => 'Rapor Bilgileri'
    ],

    'firma_adi' => [
        'label' => 'Firma Adı',
        'description' => 'Raporu hazırlayan firma adını gösterir',
        'type' => 'static',
        'value' => 'JEOTEK Zemin Araştırmaları Ltd. Şti.',
        'category' => 'Firma Bilgileri'
    ],

    'rapor_no' => [
        'label' => 'Rapor Numarası',
        'description' => 'Raporun benzersiz numarasını gösterir',
        'type' => 'sql',
        'value' => "SELECT rapor_no FROM raporlar WHERE id = {rapor_id}",
        'category' => 'Rapor Bilgileri'
    ],

    'musteri_adi' => [
        'label' => 'Müşteri Adı',
        'description' => 'Raporu talep eden müşteri adını gösterir',
        'type' => 'sql',
        'value' => "SELECT CONCAT(u.name, ' ', u.lastname) as musteri_adi FROM raporlar r LEFT JOIN users u ON r.user_id = u.user_id WHERE r.id = {rapor_id}",
        'category' => 'Müşteri Bilgileri'
    ],

    'proje_adi' => [
        'label' => 'Proje Adı',
        'description' => 'Projenin adını gösterir',
        'type' => 'sql',
        'value' => "SELECT proje_adi FROM raporlar WHERE id = {rapor_id}",
        'category' => 'Proje Bilgileri'
    ],

    'sondaj_sayisi' => [
        'label' => 'Sondaj Sayısı',
        'description' => 'Raporda yer alan toplam sondaj sayısını gösterir',
        'type' => 'sql',
        'value' => "SELECT COUNT(*) as sondaj_sayisi FROM sklar WHERE rapor_id = {rapor_id}",
        'category' => 'Sondaj Bilgileri'
    ],

    'bugun_tarihi' => [
        'label' => 'Bugünün Tarihi',
        'description' => 'Güncel tarihi gösterir',
        'type' => 'function',
        'value' => 'get_current_date',
        'category' => 'Sistem Bilgileri'
    ],

    // Örnek statik değişkenler
    'firma_telefon' => [
        'label' => 'Firma Telefon',
        'description' => 'Firma telefon numarasını gösterir',
        'type' => 'static',
        'value' => '+90 (232) 123 45 67',
        'category' => 'Firma Bilgileri'
    ],

    'firma_adres' => [
        'label' => 'Firma Adres',
        'description' => 'Firma adresini gösterir',
        'type' => 'static',
        'value' => 'İzmir, Türkiye',
        'category' => 'Firma Bilgileri'
    ]
];

/**
 * Değişken kategorileri
 * Değişkenleri gruplandırmak için kullanılır
 */
$variable_categories = [
    'Rapor Bilgileri' => 'Rapor ile ilgili temel bilgiler',
    'Arazi Bilgileri' => 'Parsel ve arazi ile ilgili bilgiler',
    'Firma Bilgileri' => 'Raporu hazırlayan firma bilgileri',
    'Müşteri Bilgileri' => 'Müşteri ile ilgili bilgiler',
    'Proje Bilgileri' => 'Proje ile ilgili bilgiler',
    'Sondaj Bilgileri' => 'Sondaj çalışmaları ile ilgili bilgiler',
    'Sistem Bilgileri' => 'Sistem tarafından otomatik oluşturulan bilgiler'
];

/**
 * Değişken değerini al
 * 
 * @param string $variable_name Değişken adı
 * @param int $rapor_id Rapor ID'si
 * @return string Değişken değeri
 */
function get_variable_value($variable_name, $rapor_id = null)
{
    global $rapor_variables, $db;

    ped($rapor_variables);
    if (!isset($rapor_variables[$variable_name])) {
        return '{' . $variable_name . '}'; // Tanımsız değişken, olduğu gibi döndür
    }
    echo 'eldim';

    $variable = $rapor_variables[$variable_name];
    pe($variable);
    switch ($variable['type']) {
        case 'static':
            return $variable['value'];

        case 'sql':
            if ($rapor_id && $db) {
                // SQL sorgusunda {rapor_id} placeholder'ını değiştir
                echo $sql = str_replace('{rapor_id}', intval($rapor_id), $variable['value']);
                $result = $db->get_var($sql);
                return $result ? $result : '';
            }
            return '';

        case 'function':
            if (function_exists($variable['value'])) {
                return call_user_func($variable['value'], $rapor_id);
            }
            return '';

        default:
            return $variable['value'];
    }
}

/**
 * Tüm değişkenleri listele
 * 
 * @return array Değişken listesi
 */
function get_all_variables()
{
    global $rapor_variables;
    return $rapor_variables;
}

/**
 * Kategoriye göre değişkenleri listele
 * 
 * @param string $category Kategori adı
 * @return array Kategorideki değişkenler
 */
function get_variables_by_category($category)
{
    global $rapor_variables;

    $filtered = [];
    foreach ($rapor_variables as $name => $variable) {
        if ($variable['category'] === $category) {
            $filtered[$name] = $variable;
        }
    }

    return $filtered;
}

/**
 * Yardımcı fonksiyonlar
 */

/**
 * Güncel tarihi döndür
 */
function get_current_date($rapor_id = null)
{
    return date('d.m.Y');
}
