-- Düzenlenebilir değ<PERSON>şkenler için tablo
CREATE TABLE IF NOT EXISTS `rapor_variables` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `variable_name` varchar(100) NOT NULL COMMENT '<PERSON><PERSON><PERSON><PERSON>ken adı',
  `variable_value` text NOT NULL COMMENT '<PERSON>ğ<PERSON><PERSON><PERSON> de<PERSON>eri',
  `rapor_id` int(11) NOT NULL DEFAULT 0 COMMENT 'Rapor ID (0 = global)',
  `isletme_id` int(11) NOT NULL COMMENT 'İşletme ID',
  `user_id` int(11) NOT NULL COMMENT 'Oluşturan kullanıcı ID',
  `created_date` datetime NOT NULL COMMENT 'Oluşturma tarihi',
  `updated_date` datetime NOT NULL COMMENT 'Güncelleme tarihi',
  PRIMARY KEY (`id`),
  KEY `variable_name` (`variable_name`),
  KEY `rapor_id` (`rapor_id`),
  KEY `isletme_id` (`isletme_id`),
  UNIQUE KEY `unique_variable` (`variable_name`, `rapor_id`, `isletme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci COMMENT='Düzenlenebilir rapor değişkenleri';

-- Varsayılan değerleri ekle
INSERT IGNORE INTO `rapor_variables` (`variable_name`, `variable_value`, `rapor_id`, `isletme_id`, `user_id`, `created_date`, `updated_date`) VALUES
('standart_giris_metni', 'Bu rapor, {firma_adi} tarafından {rapor_tarihi} tarihinde hazırlanmıştır.', 0, 1, 1, NOW(), NOW()),
('standart_sonuc_metni', 'Yapılan incelemeler sonucunda elde edilen bulgular yukarıda sunulmuştur.', 0, 1, 1, NOW(), NOW()),
('yasal_uyari', 'Bu rapor sadece belirtilen proje için geçerlidir ve başka amaçlarla kullanılamaz.', 0, 1, 1, NOW(), NOW()),
('imza_blogu', '<table style="width:100%; margin-top:50px;"><tr><td style="text-align:center; width:50%; padding:20px;">Hazırlayan:<br><br><br>____________________<br>Jeolog</td><td style="text-align:center; width:50%; padding:20px;">Onaylayan:<br><br><br>____________________<br>Sorumlu Müdür</td></tr></table>', 0, 1, 1, NOW(), NOW());
