<?php

if ($render_zemin_tanimi == 1) {
    fn_delete_row('zemin_tanimi_for_log', "rapor_id=$id");
    fn_set_notice('Yeniden tasarlandi');
    fn_redirect(action_link('log&sk_id=' . $sk_id, 1));
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // render log
    $v = $_data = array();
    $v['baslangic'] = $baslangic_round;
    $v['bitis'] = $round_bitis;

    // kontrol
    $zemin = get_row('zemin_tanimi_for_log', "id=$zemin_log_id");

    // if ($zemin->zemin_rengi != 'bitkisel-toprak') {
    //     if ($baslangic_round < 1.5) {
    //         fn_set_notice('Zemin rengi 1.5 kucuk olmaz', 'W');
    //         fn_redirect(action_link('log&sk_id=' . $sk_id, 1));
    //     }
    // }


    $v['round_bitis'] = round_zemin($v['bitis']);
    $v['baslangic_round'] = round_zemin($v['baslangic']);

    if ((($v['baslangic_round']) < $v['round_bitis'])) {
        $v['round_bitis'] = round_zemin($v['round_bitis']);
        $v['rowspan'] = true;
        $rowspan = ($v['round_bitis'] - $v['baslangic_round']) * 2;
        $v['rowspan_row'] = $rowspan;
    } else {
        $v['round_bitis'] = round_zemin($v['bitis']);
        $v['rowspan'] = false;
    }

    if (strpos($v['baslangic_round'], '.') !== false) {
        $s_key = rtrim(rtrim($v['baslangic_round'], '0'), '.');
    } else {
        $s_key = $v['baslangic_round'];
    }
    $v['render_col'] = 'row_' . str_replace('.', '_', $s_key);

    $v['derinlik_aciklama'] = $v['round_bitis'] . ' m';

    // $v['position_top'] = 0;
    // $v['position_left'] = 0;
    fn_update_array('zemin_tanimi_for_log', $v, "id=$zemin_log_id limit 1");
    fn_set_notice('Yeniden tasarlandi');

    fn_redirect(action_link('log&sk_id=' . $sk_id, 1));
}
if (empty($id) or empty($sk_id)) {
    echo 'Rapor ID ve SK ID boş olamaz';
    die;
}
$rapor = get_row('raporlar', "id=$id AND isletme_id=" . ISLETME_ID);
$musteri = get_row('users', "user_id=$rapor->customer_id");
$secili_sk = get_row('sklar', "rapor_id=$rapor->id AND id=$sk_id");
$sorumlu_muh = get_row('users', "user_id=$secili_sk->sorumlu_muh AND isletme_id=" . ISLETME_ID);
$sayfa_no = 1;
$yer_altisuyu = get_results('yeralti_sulari', "rapor_id=$rapor->id AND sk_id=$secili_sk->id limit 2", '', ARRAY_A);
// En son log
$last_log_row = get_row('log', "rapor_id=$rapor->id AND sk_id=$secili_sk->id order by id desc", 'derinlik_bitis,derinlik_baslangic');

// pe($rapor);
// pe($secili_sk);
// pe($sorumlu_muh);

$renders_zemin_tanimi = render_zemin_tanimi_to_log($rapor->id, $secili_sk->adi, $last_log_row);
$last_key = array_key_last($renders_zemin_tanimi);
$renders_zemin_tanimi[$last_key]['derinlik_aciklama'] = '';
?>
<div class="mx-auto" style="width: 1100px;">
    <table class="table table-sm  table-bordered align-center table-secondary m-0" width="100%">
        <tbody class="fw-bold">
            <tr>
                <th width="120">Logo</th>
                <th colspan="3" class="text-center fw-bold bg-secondary">SONDAJ LOGO</th>
                <th width="140" class="bg-secondary">İşveren</th>
                <th width="140" class="bg-secondary"><?php echo $musteri->firma_adi; ?></th>
            </tr>
            <tr>
                <th width="120" class="bg-secondary text-center">Proje Adı</th>
                <th colspan="5" class="text-center fw-bold"><?php echo $rapor->proje_adi; ?></th>
            </tr>
            <tr>
                <th width="120" class="text-center bg-secondary">İl</th>
                <th width="400" class="text-center fw-normal"><?php echo $rapor->il; ?></th>
                <th width="300" class="text-center bg-secondary">Sondaj Derinliği(m)</th>
                <th class="text-center fw-normal"><?php echo $secili_sk->sondaj_derinligi ?></th>
                <th class="text-start bg-secondary">Sondaj No</th>
                <th class="text-center"><?php echo $secili_sk->adi ?></th>
            </tr>
            <tr>
                <th width="120" class="text-center bg-secondary">İlçe</th>
                <th width="400" class="text-center fw-normal"><?php echo $rapor->ilce; ?></th>
                <th width="300" class="text-center bg-secondary">Başlama Tarihi</th>
                <th class="text-center fw-normal"><?php echo unix_to_date($secili_sk->baslama_tarihi, 1); ?></th>
                <th class="text-start bg-secondary">Sayfa No</th>
                <th class="text-center"><?php echo $sayfa_no ?></th>
            </tr>
            <tr>
                <th width="120" class="text-center bg-secondary">Mahalle/Köy</th>
                <th width="400" class="text-center fw-normal"><?php echo $rapor->mahalle; ?></th>
                <th width="300" class="text-center bg-secondary">Bitiş Tarihi</th>
                <th class="text-center fw-normal"><?php echo unix_to_date($secili_sk->baslama_tarihi, 1); ?></th>
                <th colspan="2" class="text-center bg-secondary">Sorumlu Jeoloji Müh.</th>
            </tr>
            <tr>
                <th width="120" class="text-center bg-secondary">Pafta</th>
                <th width="400" class="text-center fw-normal"><?php echo $rapor->pafta; ?></th>
                <th width="300" class="text-center bg-secondary">Makine Tipi/Metodu</th>
                <th class="text-center fw-normal"><?php echo $secili_sk->makine_tipi; ?></th>
                <th colspan="2" class="text-center"><?php echo $sorumlu_muh->name; ?> <?php echo $sorumlu_muh->lastname; ?></th>
            </tr>
            <tr>
                <th width="120" class="text-center bg-secondary">Ada</th>
                <th width="400" class="text-center fw-normal"><?php echo $rapor->ada; ?></th>
                <th width="300" class="text-center bg-secondary">SPT Şahmerdan Tipi </th>
                <th class="text-center fw-normal"><?php echo $secili_sk->sahmerdan_tipi; ?></th>
                <th colspan="2" class="text-center">JEOLOJİ MÜHENDİSİ</th>
            </tr>
            <tr>
                <th width="120" class="text-center bg-secondary">Parsel</th>
                <th width="400" class="text-center fw-normal"><?php echo $rapor->parsel; ?></th>
                <th width="300" class="text-center bg-secondary">Delgi Çapı</th>
                <th class="text-center fw-normal">ɸ<?php echo $secili_sk->delgi_capi; ?>mm</th>
                <th colspan="2" class="text-center">SİCİL NO:xxx</th>
            </tr>
            <tr>
                <th width="120" class="text-center bg-secondary">Sondaj Kotu</th>
                <th width="400" class="text-center fw-normal"><?php echo $secili_sk->sondaj_kotu; ?></th>
                <th width="300" class="text-center bg-secondary" rowspan="3" valign="middle">Yeraltı Suyu(m)</th>
                <th class="text-start border-top-0 fw-normal p-0" valign="top" colspan="3" rowspan="3">
                    <table class="table table-bordered align-center table-secondary m-0" width="100%">
                        <tr>
                            <th width="108" class="text-center bg-secondary">Derinlik</th>
                            <th width="108" class="text-center bg-secondary">Tarih</th>
                            <th width="108" class="text-center bg-secondary">Açıklama</th>
                            <th class="text-center bg-secondary">Sondör: <?php echo $sorumlu_muh->name; ?> <?php echo $sorumlu_muh->lastname; ?></th>
                        </tr>
                        <tr>
                            <th width="108" class="text-center fw-normal"><?php echo ($yer_altisuyu[0]) ? $yer_altisuyu[0]['derinlik'] . 'm' : '-' ?></th>
                            <th width="108" class="text-center fw-normal"><?php echo ($yer_altisuyu[0]) ? unix_to_date($yer_altisuyu[0]['tarih'], 1) : '-' ?></th>
                            <th width="108" class="text-center fw-normal"><?php echo ($yer_altisuyu[0]) ? $yer_altisuyu[0]['aciklama'] : '-' ?></th>
                            <th class="text-center fw-normal">Belge No</th>
                        </tr>
                        <tr>
                            <th width="108" class="text-center fw-normal"><?php echo ($yer_altisuyu[1]) ? $yer_altisuyu[1]['derinlik'] . 'm' : '-' ?></th>
                            <th width="108" class="text-center fw-normal"><?php echo ($yer_altisuyu[1]) ? unix_to_date($yer_altisuyu[1]['tarih'], 1) : '-' ?></th>
                            <th width="108" class="text-center fw-normal"><?php echo ($yer_altisuyu[1]) ? $yer_altisuyu[1]['aciklama'] : '-' ?></th>
                            <th class="text-center fw-normal"><?php echo $sorumlu_muh->belge_no; ?></th>
                        </tr>
                    </table>
                </th>
            </tr>
            <tr>
                <th width="120" class="text-center bg-secondary align-center flex-center" rowspan="2" valign="middle" align="center">Koordinatlar</th>
                <th width="400" class="text-center fw-normal"><strong>E:</strong> <?php echo $secili_sk->latitude; ?></th>
            </tr>
            <tr>
                <th width="400" class="text-center fw-normal"><strong>B:</strong> <?php echo $secili_sk->longitude; ?></th>
            </tr>
        </tbody>
    </table>
    <table class="table  table-sm  align-center  text-center table-bordered align-center table-secondary m-0" width="100%">
        <thead>
            <tr>
                <td rowspan="3" class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text">Sondaj Derinliği(m)</div>
                    </div>
                </td>
                <td rowspan="3" class="text-center" valign="bottom">
                    <div class="vertichle-container">
                        <div class="verticle-text top-50 start-75 ">Muhafaza Borusu Derinliği</div>
                    </div>
                </td>
                <td rowspan="3" class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-100 w-10px">Kuyu İçi Deneyler</div>
                    </div>
                </td>
                <td rowspan="3" class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text">Örnek Türü ve No</div>
                    </div>
                </td>
                <td rowspan="3" class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text">Örnek Türü ve No</div>
                    </div>
                </td>
                <td colspan="4" class="text-center bg-secondary fw-bold" nowrap valign="middle">Standart Penetrasyon Testi(SPT)</td>
                <td colspan="2" class="text-center bg-secondary fw-bold" rowspan="2" valign="middle">Presiyometre Deneyi</td>
                <td colspan="6" class="text-center bg-secondary fw-bold" rowspan="2" valign="middle">Kaya Özellikleri</td>
                <td colspan="6" class="text-center bg-secondary fw-bold" rowspan="3" valign="middle">Zemin Profili</td>
                <td colspan="6" class="text-center bg-secondary fw-bold" rowspan="3" valign="middle">Zemin Tanımlanması</td>
                <td colspan="6" class="text-center bg-secondary fw-bold" rowspan="3" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-50 start-75">Sondaj Derinliği(m)</div>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="4" class="text-center bg-secondary fw-bold" valign="middle">Darbe Sayısı</td>
            </tr>
            <tr>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-50 start-50  h-10px">0-15</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-50 start-50  h-10px">15-30</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-50 start-50  h-10px">30-45</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container ">
                        <div class="verticle-text top-50 start-50  h-10px ">N</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text start-25">Elastisite <br> Modülü(kg/cm2)</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text start-25">Net Limit <br> Basınç(kg/cm2)</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-50 start-0  h-10px">TCR %</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-50 start-0  h-10px">SCR %</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-50 start-0  h-10px">RQD %</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text">Ayrışma Derecesi</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text top-75 start-0 w-10px h-10px">Çatlak Sıklığı</div>
                    </div>
                </td>
                <td class="text-center" valign="middle">
                    <div class="vertichle-container">
                        <div class="verticle-text bottom-25 start-0 w-10px h-10px">Dayanım </div>
                    </div>
                </td>

            </tr>
        </thead>
        <tbody class="fs-sm">
            <?php
            $log_say = 0;
            for ($i = -0.5; $i < $last_log_row->derinlik_baslangic;):
                $i = $i + 0.5;
                $log_row = get_row('log', "rapor_id=$rapor->id AND sk_id=$secili_sk->id AND derinlik_baslangic=$i");
                $tanim = strtoupper($log_row->tanim);

                $log_say++;
            ?>
                <tr class="<?php echo ($i != $last_log_row->derinlik_baslangic) ? 'border-top-0 border-bottom-0' : ''; ?> ">
                    <td class="border-1 border-secondary"><?php echo $i; ?></td>
                    <td class="border-1 border-secondary"></td>
                    <td class="border-1 border-secondary"></td>
                    <td class="border-1 border-secondary"></td>
                    <td class="border-1 border-secondary"><?php echo $tanim; ?></td>
                    <?php
                    include 'ornek_turu/spt.php';
                    include 'ornek_turu/presiyometre.php';
                    include 'ornek_turu/karot.php';
                    include 'ornek_turu/zemin_rengi.php';
                    ?>
                </tr>
            <?php endfor; ?>
        </tbody>
    </table>
    <?php include 'log/footer.php'; ?>
</div>