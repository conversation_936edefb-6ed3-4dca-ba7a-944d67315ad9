<?php

$s_key = 'row_' . str_replace('.', '_', $i);
if (array_key_exists($s_key, $renders_zemin_tanimi)) {
    $self_zemin_tanimi = $renders_zemin_tanimi[$s_key];
    // echo 'buldum :' . $s_key;
    // echo '<br>';
} else {
    $self_zemin_tanimi = array();
}

$bg = '';
if ($self_zemin_tanimi['zemin_rengi']) {
    $bg = COMMON_W_DIR . '/assets/img/zemin_renkleri/' . $self_zemin_tanimi['zemin_rengi'] . '.png';
}

if ($self_zemin_tanimi) {
    $baslangic = $self_zemin_tanimi['baslangic_round'];
    $bitis = $self_zemin_tanimi['round_bitis'];
    $rowspan = ($self_zemin_tanimi['rowspan']) ? true : false;
    $rowspan_row_bitis = ($self_zemin_tanimi['round_bitis']) ? $self_zemin_tanimi['round_bitis'] : 100;
} else {
    $baslangic = 0;
    $bitis = 0;
}
// pe($self_zemin_tanimi);
?>
<?php if (($baslangic == $i) && $rowspan): ?>
    <?php if ($i == $baslangic): ?>
        <td colspan="6" class="p-0" <?php echo ($i == $baslangic) ? 'rowspan="' . $self_zemin_tanimi['rowspan_row'] . '"' : ''; ?> valign="middle" <?php if ($bg): ?> style="background-image:url('<?php echo T . $bg . '&w=112&h=40&z=1'; ?>')" <?php endif; ?>>
        </td>
        <td class="p-0 position-relative border-0 draggable-zone " colspan="6" <?php echo ($i == $baslangic) ? 'rowspan="' . $self_zemin_tanimi['rowspan_row'] . '"' : ''; ?> valign="middle">
            <div class="d-flex flex-column flex-center position-absolute w-100 h-100 top-0  start-0  text-white p-2">
                <div class="text-gray-700 fw-semibold" data-qucikupdate="zemin_tanimi_for_log,tanim" data-quick-id="id,<?php echo $self_zemin_tanimi['id']; ?>"><?php echo $self_zemin_tanimi['tanim']; ?></div>
            </div>
            <?php if ($self_zemin_tanimi['derinlik_aciklama']): ?>
                <div data-qucikupdate="zemin_tanimi_for_log,derinlik_aciklama" data-quick-id="id,<?php echo $self_zemin_tanimi['id']; ?>" contenteditable="true" style="bottom: -20px !important; top:<?php echo $self_zemin_tanimi['position_top']; ?>px; left:0px" class="draggable cursor-move z-index-2 text-gray-900 fw-semibold text-end border-top border-top-2 border-black position-absolute w-100" contenteditable=""><?php echo $self_zemin_tanimi['derinlik_aciklama']; ?></div>
            <?php endif; ?>
        </td>
    <?php endif; ?>
<?php else: ?>
    <?php if ($rowspan == false): ?>
        <td colspan="6" class="p-0" <?php if ($bg): ?> style="background-image:url('<?php echo T . $bg . '&w=112&h=40&z=1'; ?>')" <?php endif; ?>>
        </td>
        <td colspan="6">
            <span class="text-gray-700 fw-semibold"><?php echo $self_zemin_tanimi['tanim']; ?></span>
        </td>
    <?php endif; ?>
<?php endif; ?>
<?php
if ($rowspan && $i == ($rowspan_row_bitis - 0.5)) {
    $rowspan = false;
}
?>
<td class="border-1 border-secondary position-relative">
    <?php
    echo $i;
    if ($i == $baslangic && $i >= 0):
        // if ($i == $rowspan_row_bitis) {
        //     echo ' bottom ';
        // }
    ?>
        <form class="d-flex w-300px  position-absolute" style="right: -300px;top:0px;" method="post">
            <input type="hidden" name="do" value="raporlar/view" />
            <input type="hidden" name="action" value="log" />
            <input type="hidden" name="id" value="<?php echo $id; ?>" />
            <input type="hidden" name="sk_id" value="<?php echo $sk_id; ?>" />
            <input type="hidden" name="zemin_log_id" value="<?php echo $self_zemin_tanimi['id']; ?>" />
            <div class="w-70px  me-1">
                <input type="number" step=".5" min="0.0" name="baslangic_round" class="form-control form-control-sm " value="<?php echo $self_zemin_tanimi['baslangic_round']; ?>" placeholder="" />
            </div>
            <div class="w-70px  me-1">
                <input type="number" step=".5" min="0.0" max="100.5" name="round_bitis" class="form-control form-control-sm ps-2" value="<?php echo $self_zemin_tanimi['round_bitis']; ?>" placeholder="" />
            </div>
            <div class="w-50px">
                <button class="btn btn-secondary btn-sm rounded-0 btn-evo-loading">Kaydet</button>
            </div>
        </form>
    <?php endif; ?>
</td>