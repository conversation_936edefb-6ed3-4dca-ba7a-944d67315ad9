<?php

if ($auto_add) {

    // Var olanlari sil
    fn_delete_row('zemin_tanimi', "rapor_id=$id AND isletme_id=" . ISLETME_ID);

    $lab_sk = get_results('laboratuvar', "tur='diger' AND rapor_id=$id AND isletme_id=" . ISLETME_ID);

    $kuyular = array();
    foreach ($lab_sk as $lab) {
        $kuyular[$lab->sondaj_no][] = $lab;
    }
    $onceki_bitis = 0;
    foreach ($kuyular as $kuyu => $v) {
        foreach ($v as $k => $row) {

            // Birinci kuyu ise, baslangic zemini olarak veri olustur.
            if ($k == 0) {
                $_data = array();
                $_data['rapor_id'] = $id;
                $_data['sk'] = $kuyu;

                $derinlik_baslangic = str_replace(',', '.', 0);
                $derinlik_bitis = str_replace(',', '.', 1);
                $tanim = 'Bitkisel Toprak';
                $_data['baslangic'] = $derinlik_baslangic;
                $_data['bitis'] = $derinlik_bitis;
                $_data['tanim'] = $tanim;
                $_data['zemin_rengi'] = 'bitkisel-toprak';
                $_data['position'] = set_postion('zemin_tanimi', 1);
                $_data['type'] = 'spt';
                $_data['ekleyen'] = 'otomatik';
                $_data['numune_no'] = '0';
                fn_insert_array('zemin_tanimi', $_data);

                $onceki_bitis = $derinlik_bitis;
            }
            // echo 'k : ' . $k;
            // echo '<br>';
            // echo 'onceki_bitis : ' . $onceki_bitis;
            // echo '<br>';

            $_data = array();
            $_data['rapor_id'] = $id;
            $_data['sk'] = $kuyu;
            list($derinlik_baslangic, $derinlik_bitis) = explode('-', $row->derinlik);
            $derinlik_baslangic = str_replace(',', '.', $derinlik_baslangic);
            $derinlik_bitis = str_replace(',', '.', $derinlik_bitis);
            $tanim = zemin_tanimlamasi_spt($row->tane_buyuklugu_10, $row->tane_buyuklugu_200, $row->likit_limit);
            $_data['baslangic'] = $onceki_bitis;
            // Derinlik bitisi bir sonraki
            $_data['bitis'] = $derinlik_bitis;

            $_data['tanim'] = $tanim;
            $_data['position'] = set_postion('zemin_tanimi', 1);
            $_data['type'] = 'spt';
            $_data['ekleyen'] = 'otomatik';
            $_data['numune_no'] = $row->numune_no;
            $_data['zemin_sinifi'] = $row->zemin_sinifi;
            // Zeynep bastik everybody one more time.
            $_data['zemin_rengi'] = zemin_rengi_belirle($row->zemin_sinifi);

            fn_insert_array('zemin_tanimi', $_data);

            $onceki_bitis = $derinlik_bitis;
        }
    }
    $lab_karot = get_results('laboratuvar', "tur='karot' AND rapor_id=$id AND isletme_id=" . ISLETME_ID);

    $kuyular_karot = array();
    foreach ($lab_karot as $lab) {
        $kuyular_karot[$lab->sondaj_no][] = $lab;
    }

    $onceki_bitis = 0;
    foreach ($kuyular_karot as $kuyu => $v) {
        foreach ($v as $k => $row) {
            $_data = array();
            $_data['rapor_id'] = $id;
            $_data['sk'] = $kuyu;
            list($derinlik_baslangic, $derinlik_bitis) = explode('-', $row->derinlik);
            $derinlik_baslangic = str_replace(',', '.', $derinlik_baslangic);
            $derinlik_bitis = str_replace(',', '.', $derinlik_bitis);
            $tanim = zemin_tanimlamasi_karot($row->tane_buyuklugu_10, $row->tane_buyuklugu_200);
            $_data['baslangic'] = $derinlik_baslangic;
            $_data['bitis'] = $derinlik_bitis;
            $_data['tanim'] = $tanim;
            $_data['position'] = set_postion('zemin_tanimi', 1);
            $_data['type'] = 'karot';
            $_data['ekleyen'] = 'otomatik';
            $_data['numune_no'] = $row->numune_no;
            $_data['zemin_sinifi'] = $row->zemin_sinifi;
            fn_insert_array('zemin_tanimi', $_data);
        }
    }

    // Labarotuara gonderilmeyen numuneler bir onceki ile birlestirilmelidir.
    // die;
    fn_set_notice('Zemin raporu oluşturuldu.');
    fn_redirect(action_link('test', 1));
} else {

    $lab_karot = get_results('zemin_tanimi', "rapor_id=$id AND type='karot' AND isletme_id=" . ISLETME_ID . " order by position ");
    $lab_kuyulari = $kuyular_karot = array();

    foreach ($lab_karot as $lab) {
        $kuyular_karot[$lab->sk][] = $lab;
    }
    $lab_st = get_results('zemin_tanimi', "rapor_id=$id AND type='spt' AND isletme_id=" . ISLETME_ID . " order by position ");
    $kuyular = array();
    foreach ($lab_st as $lab) {
        $kuyular[$lab->sk][] = $lab;
        $lab_kuyulari[$lab->sk][] = $lab->numune_no;
    }
}

// Tum kuyular
$_kuyular = get_results('sklar', "rapor_id=$id AND isletme_id=" . ISLETME_ID, 'id,adi');
$kuyular_listedi = array();
foreach ($_kuyular as $kuyu) {
    $log = get_results('log', "sk_id=$kuyu->id", 'id,tanim');
    foreach ($log as $lo) {
        $kuyular_listedi[$kuyu->adi][] = $lo->tanim;
    }
}

$lab_sk = get_results('laboratuvar', "tur='diger' AND rapor_id=$id AND isletme_id=" . ISLETME_ID);

$_labl_kuyular = array();
foreach ($lab_sk as $lab) {
    $_labl_kuyular[$lab->sondaj_no][] = $lab;
}

$lab_kuyulari_sk = array();
foreach ($_labl_kuyular as $kuyu => $v) {
    foreach ($v as $k => $row) {
        $lab_kuyulari_sk[$kuyu][] = $row->numune_no;
    }
}
// pe($kuyular_listedi);
// pe($lab_kuyulari_sk);

$onceki_spt = '';
foreach ($kuyular_listedi as $kuyu => $sptler) {
    foreach ($sptler as $spt) {
        if ($lab_kuyulari_sk[$kuyu]) {
            if (in_array($spt, $lab_kuyulari_sk[$kuyu]) or (strpos($spt, 'UD') !== false)) {
                // echo 'bulundu : ' . $spt;
                // echo '<br>';
            } else {
                // Eklenecek veri:
                $_data = array();
                // echo 'sptk  : ' . $onceki_spt;
                // var_dump(strpos($onceki_spt, 'UD') !== false);
                if (strpos($onceki_spt, 'UD') !== false) {
                    $onceki_array = get_row('zemin_tanimi', "rapor_id=$id AND sk='$kuyu' AND numune_no='UD'", '*', ARRAY_A);
                } else {
                    $onceki_array = get_row('zemin_tanimi', "rapor_id=$id AND sk='$kuyu' AND numune_no='$onceki_spt'", '*', ARRAY_A);
                }

                // pe($onceki_array);
                $log_bilgisi = get_row('log', "rapor_id=$id AND tanim='$spt'");
                $_data = $onceki_array;
                $_data['id'] = NULL;
                $_data['numune_no'] = $spt;
                $_data['position'] = $onceki_array['position'] + 1;
                $_data['baslangic'] = $log_bilgisi->derinlik_baslangic;
                $_data['bitis'] = $log_bilgisi->derinlik_bitis;
                $_data['icerik'] = 'merge';
                // pe($_data);
                // echo 'Eklenecek ' . $spt . ' from : ' . $onceki_spt;
                // echo '<br>';
                // fn_insert_array('zemin_tanimi', $_data);
            }
            $onceki_spt = $spt;
        }
    }
}
// pe($kuyular);
?>
<div class="row">
    <div class="col-xl-12 col-xxl-12">
        <div class="card card-flush mb-10 ">
            <div class="card-header">
                <div class="card-title">
                    <h3>SPT</h3>
                </div>
                <div class="card-toolbar">
                    <a class="btn btn-icon btn-sm btn-secondary w-50px h-25px rounded " data-bs-toggle="modal" data-bs-target="#kt_modal_1">
                        <i class="ki-outline ki-plus fs-4"></i>
                    </a>
                </div>
            </div>
            <div class="card-body pt-0">
                <?php if ($kuyular): ?>
                    <?php foreach ($kuyular as $kuyu => $v):   ?>
                        <h5 class="mt-5"><?php echo $kuyu; ?></h5>
                        <div class="table-responsive">
                            <?php foreach ($v as $k => $row) {
                                if ($row->icerik == 'merge') continue;
                                include 'inc/giris.php';
                            }
                            ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="alert alert-dismissible bg-light d-flex flex-center flex-column py-10 px-10 px-lg-20 mb-10">
                        <div class="text-center mt-20 mb-9 fw-semibold text-gray-700">
                            Kayıtlı sonuç bulunamadı.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="">
        <?php
        // $zemin_tanimlari = get_results('zemin_tanimi', "rapor_id=$rapor->id AND sk='SK-1'");
        // $render_zemin_tanimlari = array();
        // render_zemin_tanimi_to_log($zemin_tanimlari)l
        // $renders_zemin_tanimi = render_zemin_tanimi_to_log($rapor->id, 'SK-1');
        // pe($renders_zemin_tanimi);
        ?>
    </div>
    <div class="col-xl-12 col-xxl-12">
        <div class="card card-flush mb-10 ">
            <div class="card-header">
                <div class="card-title">
                    <h3>Karot</h3>
                </div>
                <div class="card-toolbar">
                    <a class="btn btn-icon btn-sm btn-secondary w-50px h-25px rounded " data-bs-toggle="modal" data-bs-target="#kt_modal_1">
                        <i class="ki-outline ki-plus fs-4"></i>
                    </a>
                </div>
            </div>
            <div class="card-body pt-0">
                <?php if ($kuyular_karot): ?>
                    <?php foreach ($kuyular_karot as $kuyu => $v): ?>
                        <h5 class="mt-5"><?php echo $kuyu; ?></h5>
                        <div class="table-responsive">
                            <?php
                            foreach ($v as $k => $row) include 'inc/giris.php';
                            ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="alert alert-dismissible bg-light d-flex flex-center flex-column py-10 px-10 px-lg-20 mb-10">
                        <div class="text-center mt-20 mb-9 fw-semibold text-gray-700">
                            Kayıtlı sonuç bulunamadı.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" tabindex="-1" id="kt_modal_1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form class="form valid_form novalide" method="POST">
                <input type="hidden" name="do" value="<?php echo $target; ?>/<?php echo $mode ?>" />
                <input type="hidden" name="action" value="<?php echo $action; ?>" />
                <input type="hidden" name="islem" value="zemin_tanim_ekle" />
                <input type="hidden" name="id" value="<?php echo ($id) ? $id : 0; ?>" />
                <div class="modal-header">
                    <h3 class="modal-title">Zemin Tanımı Ekle</h3>

                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                    </div>
                    <!--end::Close-->
                </div>

                <div class="modal-body">
                    <div class="d-flex">
                        <div class="w-90px mb-4 me-4">
                            <label class=" form-label ">Sıralama: <?php question_toooltip('Boş bırakırsanız listesinin en sonuna ekler.') ?></label>
                            <input type="text" name="position" class="form-control text-center mb-2 mb-md-0 " value="" placeholder="" />
                        </div>
                        <div class="mb-4 w-250px">
                            <label class=" form-label ">Tür:</label>
                            <select class="form-select" name="type" data-control="select2" data-hide-search="true" data-placeholder="Türünü Seçiniz" required>
                                <option></option>
                                <option value="spt">Spt</option>
                                <option value="karot">Karot</option>
                            </select>
                        </div>
                    </div>

                    <div class="d-flex">

                        <div class="w-100px mb-4 me-2">
                            <label class=" form-label ">Numune No:</label>
                            <input type="text" name="numune_no" class="form-control mb-2 mb-md-0 ps-2" value="" placeholder="" required />
                        </div>
                        <div class="w-100px mb-4 me-2">
                            <label class=" form-label ">Başlangıç:</label>
                            <input type="number" step=".5" min="0.0" max="100.5" name="baslangic" class="form-control mb-2 mb-md-0 ps-2" value="" placeholder="" required />
                        </div>
                        <div class="w-100px mb-4 me-2">
                            <label class=" form-label ">Bitiş:</label>
                            <input type="number" step=".5" min="0.0" max="100.5" name="bitis" class="form-control mb-2 mb-md-0 ps-2" value="" placeholder="" required />
                        </div>
                    </div>
                    <div class="w-275px mb-4 ">
                        <label class=" form-label ">Zemin Profili:</label>
                        <div class="border rounded">
                            <select class="form-select toprak_rekleri form-select-transparent" name="zemin_rengi" data-placeholder="Toprak rengi seciniz" required>
                                <option></option>
                                <option value="yesil-bitkisel-toprak" data-kt-rich-content-icon="<?php echo COMMON_W_DIR ?>/assets/img/yesil-bitkisel-toprak.png">Yeşil Bitkisel Toprak</option>
                                <option value="kahverengi-bitkisel-toprak" data-kt-rich-content-icon="<?php echo COMMON_W_DIR ?>/assets/img/kahverengi-bitkisel-toprak.png">Kahverengi Bitkisel Toprak</option>
                            </select>
                        </div>
                    </div>

                    <div class="w-275px mb-4 ">
                        <label class=" form-label ">Açıklama:</label>
                        <textarea name="tanim" class="form-control mb-2 mb-md-0 ps-2 autosize  text-center" rows="1" required></textarea>
                    </div>

                </div>

                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="submit" class="btn btn-primary btn-evo-loading">Verileri Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>