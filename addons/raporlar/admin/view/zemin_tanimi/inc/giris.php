<?php

?><div class="d-flex ps-6 <?php echo ($row->icerik == 'merge') ? 'bg-light' : ''; ?>">
    <div class="d-flex align-items-center me-4">
        <button class="btn btn-icon btn-sm  btn-light-danger  w-20px h-20px rounded s_confirm" data-text="Bu satırı silmek istediğinden emin misin? Bu işlem geri alınamaz." href="<?php action_link('delete_zemin_tanimi&zemin_id=' . $row->id); ?>" data-id="<?php echo $row->id; ?>">
            <i class="ki-outline ki-cross fs-6"></i>
        </button>
    </div>
    <div class="w-100px mb-4 me-2">
        <label class=" form-label ">Numune:</label>
        <div class="d-flex bg-light flex-center py-4 h6 m-0 p-0"><?php echo $row->numune_no; ?></div>
    </div>
    <div class="w-100px mb-4 me-2">
        <label class=" form-label ">Sıralama:</label>
        <input type="number" data-qucikupdate="zemin_tanimi,position" data-quick-id="id,<?php echo $row->id; ?>" name="position" class="form-control text-center mb-2 mb-md-0 " value="<?php echo $row->position; ?>" placeholder="" />
    </div>
    <?php include 'renk_secenekleri.php'; ?>
    <div class="w-100px mb-4 me-2">
        <label class=" form-label ">Başlangıç:</label>
        <input type="number" step=".5" min="0.0" data-qucikupdate="zemin_tanimi,baslangic" data-quick-id="id,<?php echo $row->id; ?>" max="100.5" name="baslangic" class="form-control mb-2 mb-md-0 ps-2" value="<?php echo $row->baslangic; ?>" placeholder="" />
    </div>
    <div class="w-100px mb-4 me-2">
        <label class=" form-label ">Bitiş:</label>
        <input type="number" step=".5" min="0.0" data-qucikupdate="zemin_tanimi,bitis" data-quick-id="id,<?php echo $row->id; ?>" max="100.5" name="bitis" class="form-control mb-2 mb-md-0 ps-2" value="<?php echo $row->bitis; ?>" placeholder="" />
    </div>
    <div class="w-250px mb-4 me-2">
        <label class=" form-label ">Açıklama:</label>
        <textarea name="tanim" data-qucikupdate="zemin_tanimi,tanim" data-quick-id="id,<?php echo $row->id; ?>" class="form-control mb-2 mb-md-0 ps-2 autosize  text-center" rows="1"><?php echo $row->tanim; ?></textarea>
    </div>
    <div class="w-100px mb-4 me-2">
        <label class=" form-label ">Zemin Sınıfı:</label>
        <input name="tanim" type="text" data-qucikupdate="zemin_tanimi,zemin_sinifi" data-quick-id="id,<?php echo $row->id; ?>" class="form-control mb-2 mb-md-0 ps-2 autosize  text-center" value="<?php echo $row->zemin_sinifi; ?>"></input>
    </div>
    <div class="w-200px mb-4 me-2 pt-6">
        <?php if ($row->icerik == 'merge'): ?>
            <div>Otomatik oluşturuldu.</div>
        <?php endif; ?>
    </div>
</div>