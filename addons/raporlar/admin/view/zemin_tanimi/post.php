<?php
if ($islem == 'delete_zemin_tanimi') {
    fn_delete_row('zemin_tanimi', "rapor_id=$id AND id=$zemin_id AND isletme_id=" . ISLETME_ID);
    fn_set_notice('<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON> silindi');
    fn_redirect(action_link('basarili', 1));
}
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($islem == 'zemin_tanim_ekle') {
        $data = $_POST;
        $data['rapor_id'] = $data['id'];
        unset($data['id']);

        if (empty($data['position'])) {
            $data['position'] = set_postion('zemin_tanimi', 1);
        }

        fn_insert_array('zemin_tanimi', $data);
        fn_set_notice('Veri eklendi');
        fn_redirect(action_link('basarili', 1));
    }
}
