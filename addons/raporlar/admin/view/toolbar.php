<?php
$rapor = get_row('raporlar', "id=$id AND isletme_id=" . ISLETME_ID);
if (!$rapor) {
    fn_set_notice('Rapor bulunamadi', 'W');
    fn_redirect($target . '/manage');
}
$musteri = get_row('users', "user_id=$rapor->customer_id");
// $muhendis = get_row('users', "user_id=$rapor->sorumlu_muh");
$sorumlu_muh_ids = array_map(
    function ($v) {
        return $v['sorumlu_muh'];
    },
    get_results('sklar', "rapor_id=$id GROUP BY sorumlu_muh", 'sorumlu_muh', ARRAY_A)
);
if ($sorumlu_muh_ids)
    $muhendisler = get_results('users', "user_id IN (" . implode(',', $sorumlu_muh_ids) . ")");
else
    $muhendisler = array();

$sk_ozet = get_results("log", "rapor_id=$id GROUP BY ornek_turu", "COUNT(id) toplam,ornek_turu");
$toplam_kutu = say(get_results('sklar', "rapor_id=$id", 'id'));
?>
<div class="card mb-6 mb-xl-6">
    <div class="card-body pt-5 pb-0">
        <div class="d-flex flex-wrap flex-sm-nowrap mb-0">
            <?php if ($egitim->avatar): ?>
                <div class="d-flex flex-center flex-shrink-0 bg-light rounded w-100px h-100px w-lg-150px h-lg-150px me-7 p-2 mb-4">
                    <img class="img-fluid rounded" src="<?php echo $egitim_img; ?>" alt="image" />
                </div>
            <?php endif; ?>
            <!--begin::Wrapper-->
            <div class="flex-grow-1">
                <!--begin::Head-->
                <div class="d-flex justify-content-between align-items-start flex-wrap mb-0">
                    <!--begin::Details-->
                    <div class="d-flex flex-column">
                        <!--begin::Status-->
                        <div class="d-flex align-items-center mb-1">
                            <a href="#" class="text-gray-800 text-hover-primary fs-2 fw-bold me-3"><?php echo $rapor->proje_adi; ?></a>
                        </div>
                        <!--end::Status-->
                        <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                            <a href="#" class="d-flex align-items-center text-gray-500 text-hover-primary me-5 mb-2">
                                <i class="ki-duotone ki-profile-circle fs-4 me-1"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i> <?php echo $musteri->name . ' ' . $musteri->lastname; ?> (<?php echo $musteri->firma_adi ?>)
                            </a>
                            <a href="https://maps.google.com/?q=<?php echo $rapor->latitude; ?>,<?php echo $rapor->longitude; ?>&z=8" class="d-flex align-items-center text-gray-500 text-hover-primary me-5 mb-2">
                                <i class="ki-duotone ki-geolocation fs-4 me-1"><span class="path1"></span><span class="path2"></span></i> <?php echo $rapor->il; ?>, <?php echo $rapor->ilce; ?> <?php echo $rapor->mahalle; ?>
                            </a>
                            <a target="_blank" href="https://maps.google.com/?q=<?php echo $rapor->latitude; ?>,<?php echo $rapor->longitude; ?>&z=8" class="d-flex align-items-center text-gray-500 text-hover-primary mb-2">
                                <i class="ki-duotone fs-4 ki-map me-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i> <?php echo $rapor->latitude; ?>,<?php echo $rapor->longitude; ?>
                            </a>
                        </div>
                    </div>
                    <!--end::Details-->
                    <div>
                        <?php if ($muhendisler):  ?>
                            <div class="symbol-group symbol-hover ">
                                <?php foreach ($muhendisler as $a => $b):
                                    $image_url = ($b->user_type == 'Provider') ? images_url('sondorler', 'url') : images_url('employee', 'url');
                                ?>

                                    <div class="symbol symbol-35px symbol-circle" data-bs-toggle="tooltip" title="<?php echo $b->name . ' ' . $b->lastname; ?>">
                                        <?php if ($b->avatar): ?>
                                            <img alt="Pic" src="<?php echo T . $image_url . $b->avatar . '&w=120' ?>" />
                                        <?php else: ?>
                                            <span class="symbol-label bg-warning text-inverse-warning fw-bold"><?php echo $b->name[0] ?></span>
                                        <?php endif; ?>
                                        <span class="fw-bold ps-2 text-gray-600"><?php echo $b->name . ' ' . $b->lastname; ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </div>
                    <!--begin::Actions-->
                    <div class="d-flex mb-4">
                        <a href="<?php fn_menu_link('raporlar/update&id=' . $id) ?>" class="btn btn-sm btn-bg-light btn-active-color-primary me-2">
                            <i class="ki-duotone ki-pencil fs-4"><span class="path1"></span><span class="path2"></span></i>Raporu düzenle</a>
                    </div>
                    <!--end::Actions-->
                </div>

            </div>
            <!--end::Wrapper-->
        </div>

        <div class="separator"></div>
        <?php
        $action = ($action) ? $action : 'sk';
        $sub_menu = array();
        $sub_menu['sk'] = 'Sondaj Kuyuları';
        $sub_menu['laboratuvar'] = 'Laboratuvar';
        $sub_menu['zemin_tanimi'] = 'Zemin Tanımlanması';
        // $sub_menu['log'] = 'Log';
        $sub_menu['sondaj_kabul'] = 'Sondaj Kabul Tutanağı';
        $sub_menu['document'] = 'Dökümanlar';
        $sub_menu['rapor'] = 'Rapor';

        $sk_listesi = get_results('sklar', "rapor_id=$id AND isletme_id=" . ISLETME_ID);
        ?>
        <div class="d-flex justify-content-between">

            <!--begin::Nav-->
            <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold">
                <?php foreach ($sub_menu as $k => $v):  ?>

                    <li class="nav-item">
                        <a class="nav-link text-active-primary py-5 me-6 <?php echo ($k == $action) ? 'active' : ''; ?>" href="<?php fn_menu_link($target . '/view&action=' . $k . '&id=' . $id) ?>"><?php echo $v; ?></a>
                    </li>
                <?php endforeach; ?>
            </ul>
            <!--end::Nav-->
            <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold">
                <?php if ($action == 'log' && $sk_id): ?>
                    <li class="nav-item">
                        <span class="nav-link" href="">
                            <a href="<?php fn_menu_link($target . '/view&action=log&id=' . $id . '&sk_id=' . $sk_id . '&render_zemin_tanimi=1') ?>" class="btn d-flex btn-sm btn-dark">Render</a>
                        </span>
                    </li>
                <?php endif; ?>
                <li class="nav-item">
                    <!--begin::Toggle-->
                    <a data-kt-menu-trigger="hover" data-kt-menu-placement="bottom-start" data-kt-menu-offset="0px, 0px" class="nav-link text-active-primary py-5 me-6 rotate <?php echo ($k == $action) ? 'active' : ''; ?>" href="#">Log <i class="ki-duotone ki-down fs-3 rotate-180 ms-3 me-0"></i></a>
                    <!--end::Toggle-->

                    <!--begin::Menu-->
                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-auto min-w-200 mw-300px" data-kt-menu="true">
                        <div class="menu-item px-3">
                            <div class="menu-content fs-6 text-gray-900 fw-bold px-3 py-4">Sondaj Kutu Listesi</div>
                        </div>
                        <div class="separator mb-3 opacity-75"></div>
                        <?php if ($sk_listesi): ?>
                            <?php foreach ($sk_listesi as $sx => $s): ?>
                                <div class="menu-item px-3">
                                    <a href="<?php fn_menu_link($target . '/view&action=log&id=' . $id . '&sk_id=' . $s->id) ?>" class="menu-link px-3 force-popup <?php echo ($sk_id == $s->id && $action == 'log') ? 'active' : ''; ?>">
                                        <?php echo $s->adi; ?>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="menu-item px-3">
                                <div class="menu-content fs-6 text-gray-900 fw-bold px-3 py-4">Kayıtlı SK bulunamadı</div>
                            </div>
                        <?php endif; ?>

                    </div>
                    <!--end::Menu-->
                </li>
            </ul>
        </div>
    </div>
</div>