<div class="row">
    <?php
    $sonuclar = get_results('laboratuvar', "rapor_id=$id AND tur='diger' AND isletme_id=" . ISLETME_ID . " ORDER BY sondaj_no asc");
    $sonuclar_karot = get_results('laboratuvar', "rapor_id=$id AND tur='karot' AND isletme_id=" . ISLETME_ID);
    ?>
    <div class="col-md-8">
        <h2 class="ps-2 mb-5">Sonuçlar</h2>
        <div class="card card-flush py-4 mb-10 ">
            <div class="card-header">
                <div class="card-title">
                    <h3>SPT</h3>
                </div>
                <div class="card-toolbar">
                    <a class="btn btn-icon btn-sm btn-secondary w-50px h-25px rounded " data-bs-toggle="modal" data-bs-target="#kt_modal_1">
                        <i class="ki-outline ki-plus fs-4"></i>
                    </a>
                </div>
            </div>
            <div class="card-body pt-0">
                <?php if ($sonuclar): ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-bordered">
                            <tbody>
                                <tr>
                                    <th>Sondaj / Kuyu No</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td>
                                            <div class="d-flex justify-content-between">
                                                <div data-qucikupdate="laboratuvar,sondaj_no" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->sondaj_no; ?> </div>
                                                <a data-text="Bu satırı silmek istiyorsunuz. Bu işlem geri alınamaz ve bir çok şeyi etkileyebilir." class="btn btn-icon btn-sm  btn-secondary  w-20px h-20px rounded s_confirm" href="<?php action_link('sonuc_sil&lab_id=' . $v->id) ?>">
                                                    <i class="ki-outline text-danger ki-cross fs-6"></i>
                                                </a>
                                            </div>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Numune No</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,numune_no" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->numune_no; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Derinlik</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,derinlik" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->derinlik; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Su Muhtevası</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,su_muhtevasi" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->su_muhtevasi; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>10 Nolu Elekte Kalan (%)</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,tane_buyuklugu_10" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->tane_buyuklugu_10; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>200 Nolu Elekten Geçen (%)</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,tane_buyuklugu_200" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->tane_buyuklugu_200; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Likit Limitin</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,likit_limit" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->likit_limit; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>PL (%)</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,plastik_limit_pl" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->plastik_limit_pl; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>PI (%)</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,plastik_limit_pi" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->plastik_limit_pi; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Zemin Sınıfı</th>
                                    <?php foreach ($sonuclar as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,zemin_sinifi" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->zemin_sinifi; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-dismissible bg-light d-flex flex-center flex-column py-10 px-10 px-lg-20 mb-10">
                        <div class="text-center mt-20 mb-9 fw-semibold text-gray-700">

                            Kayıtlı labaratuar sonucu bulunamadı.

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="card card-flush py-4 mb-10 ">
            <div class="card-header">
                <div class="card-title">
                    <h3>Karot</h3>
                </div>
                <div class="card-toolbar">
                    <a class="btn btn-icon btn-sm btn-secondary w-50px h-25px rounded " data-bs-toggle="modal" data-bs-target="#kt_modal_karot">
                        <i class="ki-outline ki-plus fs-4"></i>
                    </a>
                </div>
            </div>
            <div class="card-body pt-0">
                <?php if ($sonuclar_karot): ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-bordered">
                            <tbody>
                                <tr>
                                    <th>Sondaj / Kuyu No</th>
                                    <?php foreach ($sonuclar_karot as $k => $v): ?>
                                        <td>
                                            <div class="d-flex justify-content-between">
                                                <div data-qucikupdate="laboratuvar,sondaj_no" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->sondaj_no; ?> </div>
                                                <a data-text="Bu satırı silmek istiyorsunuz. Bu işlem geri alınamaz ve bir çok şeyi etkileyebilir." class="btn btn-icon btn-sm  btn-secondary  w-20px h-20px rounded s_confirm" href="<?php action_link('sonuc_sil&lab_id=' . $v->id) ?>">
                                                    <i class="ki-outline text-danger ki-cross fs-6"></i>
                                                </a>
                                            </div>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Numune No</th>
                                    <?php foreach ($sonuclar_karot as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,numune_no" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->numune_no; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Derinlik</th>
                                    <?php foreach ($sonuclar_karot as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,derinlik" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->derinlik; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Doğal Birim Hacim Ağırlık</th>
                                    <?php foreach ($sonuclar_karot as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,karot_hacim_agirlik" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->karot_hacim_agirlik; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Nokta Yükü MPa</th>
                                    <?php foreach ($sonuclar_karot as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,nokta_yuku_mpa" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->nokta_yuku_mpa; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th>Nokta Yükü Kg/cm2</th>
                                    <?php foreach ($sonuclar_karot as $k => $v): ?>
                                        <td data-qucikupdate="laboratuvar,nokta_yuku_kg" data-quick-id="id,<?php echo $v->id; ?>" contenteditable="true"><?php echo $v->nokta_yuku_kg; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-dismissible bg-light d-flex flex-center flex-column py-10 px-10 px-lg-20 mb-10">
                        <div class="text-center mt-20 mb-9 fw-semibold text-gray-700">

                            Kayıtlı labaratuar sonucu bulunamadı.

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <form class="form valid_form col-md-4" method="POST" enctype="multipart/form-data">
        <input type="hidden" name="do" value="<?php echo $target; ?>/<?php echo $mode ?>" />
        <input type="hidden" name="action" value="<?php echo $action; ?>" />
        <input type="hidden" name="islem" value="upload_lab" />
        <input type="hidden" name="id" value="<?php echo ($id) ? $id : 0; ?>" />
        <div class="card card-flush py-4 mb-10">
            <div class="card-header">
                <div class="card-title">
                    <h2>Yükle</h2>
                </div>
                <div class="card-toolbar"></div>
            </div>
            <div class="card-body pt-0">

                <div class="d-flex flex-column">
                    <?php form_select('Labaratuar Firması', 'lab_firma', array('A', 'B'), true, true); ?>
                    <div class="mb-4 fv-row">
                        <label class="required form-label required">Laboratuvar Excel Dosyası </label>
                        <input class="form-control" type="file" name="excel_file" id="excel_file" value="" required>
                        <div class="text-muted pt-1 ps-2">Sadece .xls ve .xlsx dosya uzantılarına izin verilir.</div>
                    </div>
                    <label class="form-check-label mb-4 fw-bold cursor-pointer" for="flexCheckDefault">
                        <input class="form-check-input" type="checkbox" name="eski_verileri_sil" value="1" id="flexCheckDefault" />
                        Eski verileri sil ve yeniden yükle.
                    </label>
                    <label class="form-check-label mb-4 fw-bold cursor-pointer" for="flexCheckDefault">
                        <input class="form-check-input" type="checkbox" name="otomatik_zemin_tanimlama" value="1" checked="flexCheckDefault" />
                        Yükleme sonrası otomatik zemin tanımlanması yap.
                    </label>
                </div>

                <?php if ($sonuclar or $sonuclar_karot): ?>
                    <!--begin::Alert-->
                    <div class="alert alert-dismissible bg-light-danger d-flex flex-center flex-column py-10 px-10 px-lg-20 ">
                        <!--begin::Close-->
                        <button type="button" class="position-absolute top-0 end-0 m-2 btn btn-icon btn-icon-danger" data-bs-dismiss="alert">
                            <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                        </button>
                        <!--end::Close-->

                        <!--begin::Icon-->
                        <i class="ki-duotone ki-information-5 fs-5tx text-danger mb-5"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i>
                        <!--end::Icon-->

                        <!--begin::Wrapper-->
                        <div class="text-center">
                            <!--begin::Title-->
                            <h1 class="fw-bold mb-5">Uyarı</h1>
                            <!--end::Title-->

                            <!--begin::Separator-->
                            <div class="separator separator-dashed border-danger opacity-25 mb-5"></div>
                            <!--end::Separator-->

                            <!--begin::Content-->
                            <div class="mb-9 text-gray-900">Önceki kayıtlı tüm bilgileri silinecek ve yeni dosyadan üretilen veriler, tekrar entegre edilecektir.</div>
                            <!--end::Content-->
                        </div>
                        <!--end::Wrapper-->
                    </div>
                    <!--end::Alert-->
                <?php endif; ?>

            </div>
        </div>
        <div class="d-flex justify-content-end flex-center">
            <a href="#" class="btn btn-link btn-link-primary btn-back-button">Vazgeç</a>
            <span class="mx-4">or</span>
            <button type="submit" class="btn btn-primary btn-evo-loading">Yükle ve Senkron Et</button>
        </div>
    </form>
</div>

<div class="modal fade" tabindex="-1" id="kt_modal_karot">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form class="form valid_form " method="POST">
                <input type="hidden" name="do" value="<?php echo $target; ?>/<?php echo $mode ?>" />
                <input type="hidden" name="action" value="<?php echo $action; ?>" />
                <input type="hidden" name="islem" value="yeni_sonuc_ekle" />
                <input type="hidden" name="id" value="<?php echo ($id) ? $id : 0; ?>" />
                <input type="hidden" name="data[tur]" value="karot" />
                <div class="modal-header">
                    <h3 class="modal-title">Karot Sonuç Ekle</h3>

                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                    </div>
                    <!--end::Close-->
                </div>

                <div class="modal-body">
                    <table class="table table-hover table-bordered">
                        <tbody>
                            <tr>
                                <th>Sondaj / Kuyu No</th>
                                <td><input type="text" class="form-control" name="data[sondaj_no]" required></td>
                            </tr>
                            <tr>
                                <th>Numune No</th>
                                <td><input type="text" class="form-control" name="data[numune_no]" required></td>
                            </tr>
                            <tr>
                                <th>Derinlik</th>
                                <td><input type="text" class="form-control" name="data[derinlik]"></td>
                            </tr>
                            <tr>
                                <th>Doğal Birim Hacim Ağırlık</th>
                                <td><input type="text" class="form-control" name="data[karot_hacim_agirlik]"></td>
                            </tr>
                            <tr>
                                <th>Nokta Yükü MPa</th>
                                <td><input type="text" class="form-control" name="data[nokta_yuku_kg]"></td>
                            </tr>
                            <tr>
                                <th>Nokta Yükü Kg</th>
                                <td><input type="text" class="form-control" name="data[likit_limit]"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="submit" class="btn btn-primary btn-evo-loading">Verileri Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="modal fade" tabindex="-1" id="kt_modal_1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form class="form valid_form " method="POST">
                <input type="hidden" name="do" value="<?php echo $target; ?>/<?php echo $mode ?>" />
                <input type="hidden" name="action" value="<?php echo $action; ?>" />
                <input type="hidden" name="islem" value="yeni_sonuc_ekle" />
                <input type="hidden" name="id" value="<?php echo ($id) ? $id : 0; ?>" />
                <input type="hidden" name="data[tur]" value="diger" />
                <div class="modal-header">
                    <h3 class="modal-title">Sonuç Ekle</h3>

                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                    </div>
                    <!--end::Close-->
                </div>

                <div class="modal-body">
                    <table class="table table-hover table-bordered">
                        <tbody>
                            <tr>
                                <th>Sondaj / Kuyu No</th>
                                <td><input type="text" class="form-control" name="data[sondaj_no]" required></td>
                            </tr>
                            <tr>
                                <th>Numune No</th>
                                <td><input type="text" class="form-control" name="data[numune_no]" required></td>
                            </tr>
                            <tr>
                                <th>Derinlik</th>
                                <td><input type="text" class="form-control" name="data[derinlik]"></td>
                            </tr>
                            <tr>
                                <th>Su Muhtevası</th>
                                <td><input type="text" class="form-control" name="data[su_muhtevasi]"></td>
                            </tr>
                            <tr>
                                <th>10 Nolu Elekte Kalan (%)</th>
                                <td><input type="text" class="form-control" name="data[tane_buyuklugu_10]"></td>
                            </tr>
                            <tr>
                                <th>200 Nolu Elekten Geçen (%)</th>
                                <td><input type="text" class="form-control" name="data[tane_buyuklugu_200]"></td>
                            </tr>
                            <tr>
                                <th>Likit Limitin</th>
                                <td><input type="text" class="form-control" name="data[likit_limit]"></td>
                            </tr>
                            <tr>
                                <th>PL (%)</th>
                                <td><input type="text" class="form-control" name="data[plastik_limit_pl]"></td>
                            </tr>
                            <tr>
                                <th>PI (%)</th>
                                <td><input type="text" class="form-control" name="data[plastik_limit_pi]"></td>
                            </tr>
                            <tr>
                                <th>Zemin Sınıfı</th>
                                <td><input type="text" class="form-control" name="data[zemin_sinifi]"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="submit" class="btn btn-primary btn-evo-loading">Verileri Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>