<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;

if ($islem == 'sonuc_sil') {
    fn_delete_row('laboratuvar', "id=$lab_id AND isletme_id=" . ISLETME_ID);
    fn_set_notice('Veriler başarıyla silindi.');
    fn_redirect(action_link('upload', 1));
}
if ($islem == 'yeni_sonuc_ekle') {
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $data['rapor_id'] = $id;
        $data['kayit_turu'] = 'manuel';
        fn_insert_array('laboratuvar', $data);
        fn_set_notice('Veriler başarıyla sisteme eklendi.');
        fn_redirect(action_link('upload', 1));
    }
}
if ($islem == 'upload_lab') {

    // Labaroutar A

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {

        $filename = fn_file_upload($_FILES['excel_file'], images_url($target), true, array('xls', 'xlsx'));

        $file_ext = strtolower(substr($filename, strrpos($filename, '.') + 1));
        if ($filename) {

            if ($eski_verileri_sil == '1') {
                // Eski verileri sil
                fn_delete_row('laboratuvar', "rapor_id=$id AND isletme_id=" . ISLETME_ID);

                // zemin_tanimi_for_log
                fn_delete_row('zemin_tanimi_for_log', "rapor_id=$id");
            }


            $dir = LIB_DIR . DS . 'phpexcel' . DS;

            require $dir . 'vendor/autoload.php';

            $spreadsheet = new Spreadsheet();

            $inputFileType = 'Xls';
            $inputFileType = ucfirst($file_ext);
            $inputFileName = images_url($target) . $filename;
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);



            $worksheetData = $reader->listWorksheetInfo($inputFileName);


            foreach ($worksheetData as $worksheet) {

                $sheetName = $worksheet['worksheetName'];

                // echo "<h4>$sheetName</h4>";/
                /**  Load $inputFileName to a Spreadsheet Object  **/
                $reader->setLoadSheetsOnly($sheetName);
                $spreadsheet = $reader->load($inputFileName);
                ##  SAVE AS HTML
                // $writer = IOFactory::createWriter($spreadsheet, 'Html');
                // $message = $writer->save('php://output');
                ## END  SAVE AS HTML
                $worksheet = $spreadsheet->getActiveSheet();
                // $rows = $worksheet->rangeToArray("D18:K40");
                $rows = $worksheet->toArray();

                // pe($rows);
                $bakilacak = array();
                foreach ($rows as $key => $value) {

                    // ilk sirayi atla
                    if ($key == 0) continue;
                    // Bakilacak yerler
                    if (array_search('Sondaj / Kuyu No', $value)) {
                        $bakilacak[$key + 1] = array_search('Sondaj / Kuyu No', $value);
                    }
                }

                foreach ($bakilacak as $row_sondaj => $v_sondaj) {

                    $row_son = $row_sondaj + 17;
                    $sonuc = $worksheet->rangeToArray("D$row_sondaj:W$row_son");


                    // echo 'S : ' . $s;
                    // echo '<hr>';
                    foreach ($sonuc[0] as  $sor => $sondaj_listesi) {

                        if (strtolower(trim($sonuc[1][$sor])) != 'karot') {

                            if (empty(trim($sondaj_listesi))) continue;

                            $_data = array();
                            $_data['tur'] = 'diger';
                            $_data['rapor_id'] = $id;
                            $_data['kayit_turu'] = 'excel';
                            $_data['sondaj_no'] = $sondaj_listesi;
                            $_data['numune_no'] = $sonuc[1][$sor];
                            $_data['derinlik'] = $sonuc[2][$sor];
                            $_data['su_muhtevasi'] = $sonuc[3][$sor];
                            $_data['tane_buyuklugu_10'] = $sonuc[4][$sor];
                            $_data['tane_buyuklugu_200'] = $sonuc[5][$sor];
                            $_data['likit_limit'] = $sonuc[6][$sor];
                            $_data['plastik_limit_pl'] = $sonuc[7][$sor];
                            $_data['plastik_limit_pi'] = $sonuc[8][$sor];
                            $_data['zemin_sinifi'] = $sonuc[9][$sor];
                            $_data['kivam_indeksi'] = $sonuc[10][$sor];
                            // pe($_data);
                            // echo '<hr>';
                            fn_insert_array('laboratuvar', $_data);
                        } else {
                            // Karot
                            if (empty(trim($sondaj_listesi))) continue;
                            $_data = array();
                            $_data['tur'] = 'karot';
                            $_data['rapor_id'] = $id;
                            $_data['kayit_turu'] = 'excel';
                            $_data['sondaj_no'] = $sondaj_listesi;
                            $_data['numune_no'] = $sonuc[1][$sor];
                            $_data['derinlik'] = $sonuc[2][$sor];
                            $_data['karot_hacim_agirlik'] = $sonuc[3][$sor];
                            $_data['nokta_yuku_mpa'] = $sonuc[5][$sor];
                            $_data['nokta_yuku_kg'] = $sonuc[6][$sor];
                            // pe($_data);
                            // echo '<hr>';
                            fn_insert_array('laboratuvar', $_data);
                        }
                    }
                }
            }


            fn_delete_file(images_url($target) . $filename);
            fn_set_notice('Veriler başarıyla sisteme eklendi.');
            if ($otomatik_zemin_tanimlama == 1) {
                fn_redirect(fn_menu_link($target . '/view&&action=zemin_tanimi&id=' . $id . '&auto_add=1', 1));
            } else {
                fn_redirect(action_link('upload', 1));
            }
        } else {

            fn_set_notice('Excel dosyası yüklenemedi');
            fn_redirect(action_link('upload', 1));
        }
    }
}
