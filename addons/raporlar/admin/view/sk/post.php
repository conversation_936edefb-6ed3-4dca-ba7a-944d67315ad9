<?php
if ($islem == 'sk_sil') {
    fn_delete_row('sklar', "id=$sk_id AND isletme_id=" . ISLETME_ID);
    fn_delete_row('log', "sk_id=$sk_id");
    fn_delete_row('yeralti_sulari', "sk_id=$sk_id");
    fn_set_notice('Sk silindi');
    fn_redirect(action_link('yeni_sk', 1));
}
// Delete Log
if ($islem == 'delete_log') {
    fn_delete_row('log', "id=$dataId limit 1");
    send_json(array('basarili'));
}
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    // Post
    if ($islem == 'derinlik') {

        fn_delete_row('yeralti_sulari', "sk_id=" . $data['sk_id']);
        if ($row_derinlik && is_array($row_derinlik)) {
            foreach ($row_derinlik as $d) {
                $_data = array();
                $_data = $d;
                $_data['tarih'] = date_to_unix($d['tarih']);
                $_data['sk_id'] = $data['sk_id'];
                $_data['rapor_id'] = $data['rapor_id'];
                fn_insert_array('yeralti_sulari', $_data);
            }
            fn_set_notice('Yer altı su bilgisi güncellendi');
        }
        fn_redirect(action_link('yeni_sk&sk_id=' . $data['sk_id'], 1));
    }
    if ($islem == 'yeni_spt') {

        $_POST['position'] = set_postion('log');

        $t =  get_var('log', "COUNT(id)", "rapor_id=$id AND sk_id=$sk_id AND ornek_turu='$ornek_turu'");
        $_POST['tanim'] = strtoupper($ornek_turu) . '-' . $t + 1;

        $log_id = fn_insert_array('log', $_POST);
        fn_set_notice('Veriler eklendi');
        fn_redirect(action_link('spt&log_id=' . $log_id . '&sk_id=' . $sk_id . '#yeni_ekle', 1));
    }
    // POST
    if ($islem == 'yeni_sk') {

        if ($data['baslama_tarihi'])
            $data['baslama_tarihi'] = date_to_unix($data['baslama_tarihi']);
        if ($data['bitis_tarihi'])
            $data['bitis_tarihi'] = date_to_unix($data['bitis_tarihi']);

        if ($sk_id > 0) {
            fn_update_array('sklar', $data, "id=$sk_id AND isletme_id=" . ISLETME_ID);
            fn_set_notice('Sk güncellendi');
        } else {
            if ($data['eklenecek_sk_sayisi'] > 1) {
                for ($i = 0; $i < $data['eklenecek_sk_sayisi']; $i++) {
                    $toplam_sklar = get_results('sklar', "rapor_id=$data[rapor_id] AND isletme_id=" . ISLETME_ID, 'id');
                    $data['adi'] = 'SK-' . say($toplam_sklar) + 1;
                    $data['position'] = set_postion('sklar');
                    $sk_id = fn_insert_array('sklar', $data);
                }
            } else {
                $data['position'] = set_postion('sklar');
                $sk_id = fn_insert_array('sklar', $data);
            }

            fn_set_notice('Sk eklendi');
        }


        fn_redirect(action_link('yeni_sk&sk_id=' . $sk_id, 1));
    }
}
