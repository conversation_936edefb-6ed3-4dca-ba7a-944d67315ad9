<?php
$sklar = get_results('sklar', "rapor_id=$rapor->id AND isletme_id=" . ISLETME_ID);
?>
<div class="card">
    <div class="card-header ">
        <div class="card-title">
            <h3 class="text-gray-700">SK Listesi</h3>
        </div>
        <div class="card-toolbar">
            <div class="me-n3">
                <a href="<?php action_link('yeni_sk')  ?>" class="btn btn-sm btn-secondary rounded"><i class="ki-outline ki-plus fs-3"></i> Yeni</a>
            </div>
        </div>
    </div>
    <div class="card-body pt-5" id="sinav_listesi">
        <?php if ($sklar):
            foreach ($sklar as $i => $sk):
                $toplam = get_results("log", "sk_id=$sk->id GROUP BY ornek_turu", "COUNT(id) toplam,ornek_turu");

        ?>
                <div class="d-flex flex-stack mb-2 pt-2 align-items-start " data-id="<?php echo $sk->id ?>">
                    <a href="<?php action_link('spt&sk_id=' . $sk->id) ?>" class="d-flex align-items-center">
                        <div class="">
                            <span class="<?php echo ($sk_id == $sk->id) ? 'active' : ''; ?> text-active-primary fs-6 fw-bold text-gray-900 text-hover-primary mb-2"><?php echo $sk->adi; ?></span>
                            <div class="fw-semibold fs-sm text-muted">
                                <?php if ($toplam): ?>
                                    <?php foreach ($toplam as $k => $v): ?>
                                        <?php echo $v->toplam; ?> <?php echo ucfirst($v->ornek_turu); ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    Veri bulunamadı.
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                    <div class="d-flex  align-items-start">
                        <a href="<?php action_link('spt&sk_id=' . $sk->id) ?>" class="btn  btn-sm  py-1 px-2 rounded  <?php echo (($islem == 'yeni_icerik' or $islem == 'spt') && $sk_id == $sk->id) ? 'btn-primary' : 'btn-secondary'; ?>">
                            <i class="ki-solid ki-questionnaire-tablet pe-0 fs-3"></i>
                            Log
                        </a>
                        <a href="<?php action_link('yeni_sk&sk_id=' . $sk->id)  ?>" data-bs-toggle="tooltip" data-bs-custom-class="tooltip-inverse" title="<?php echo $sk->adi; ?> düzenle" class="btn btn-icon btn-sm <?php echo (($islem == 'yeni_sk') && $sk_id == $sk->id) ? 'btn-primary' : 'btn-secondary'; ?>  w-25px h-25px rounded mx-2 ">
                            <i class="ki-outline ki-pencil fs-5"></i>
                        </a>
                        <a href="<?php action_link('sk_sil&sk_id=' . $sk->id); ?>" data-text="<?php echo $sk->adi; ?> ile tüm içerik bilgileri silenecektir. Onaylıyor musunuz?" data-bs-toggle="tooltip" data-bs-custom-class="tooltip-inverse" title="<?php echo $sk->adi; ?> sil" class="btn btn-icon btn-sm btn-secondary w-25px h-25px rounded s_confirm">
                            <i class="ki-outline ki-cross fs-5"></i>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="p-4">
                <div class="text-center">
                    <div class="fw-semibold text-muted">Kayıtlı içerik bulunamadı</div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>