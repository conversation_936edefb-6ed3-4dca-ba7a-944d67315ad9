<?php
if ($sk_id) {
    $data = get_row('sklar', "id=$sk_id AND rapor_id=$rapor->id AND isletme_id=" . ISLETME_ID);
} else {
    $toplam_sklar = get_results('sklar', "rapor_id=$rapor->id AND isletme_id=" . ISLETME_ID, 'id');
    $data = new stdClass();
    $data->adi = 'SK-' . say($toplam_sklar) + 1;
}
$data->eklenecek_sk_sayisi = 1;
?>
<form class="form valid_form" method="POST">
    <input type="hidden" name="do" value="<?php echo $target; ?>/<?php echo $mode ?>" />
    <input type="hidden" name="action" value="<?php echo $action; ?>" />
    <input type="hidden" name="islem" value="<?php echo $islem; ?>" />
    <input type="hidden" name="sk_id" value="<?php echo ($data->id) ? $data->id : 0; ?>" />
    <input type="hidden" name="data[rapor_id]" value="<?php echo $rapor->id; ?>" />
    <div class="card  card-flush mb-10 shadow-sm">
        <div class="card-header collapsible cursor-pointer rotate" data-bs-toggle="collapse" data-bs-target="#kt_docs_card_collapsible">
            <div class="card-title pt-5 d-flex flex-column">
                <h3>Sondaj Bilgileri</h3>
                <span class="text-gray-500 mt-1 fw-semibold fs-6"><?php echo $data->adi; ?></span>
            </div>
            <div class="card-toolbar rotate-180">
                <i class="ki-duotone ki-down fs-1"></i>
            </div>
        </div>
        <div id="kt_docs_card_collapsible" class="collapse show">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <?php form_input('Tanım', 'adi', true, '');  ?>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-5">
                            <label class="form-label">Başlangıç Tarihi</label>
                            <?php fn_datepicker_input('baslama_tarihi'); ?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-5">
                            <label class="form-label">Bitiş Tarihi</label>
                            <?php fn_datepicker_input('bitis_tarihi'); ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-4 fv-row">
                            <label class=" form-label required">Sorumlu Mühendis</label>
                            <?php
                            $sondorler = get_results('users', "user_type='Provider' AND is_delete=0 AND isletme_id=" . ISLETME_ID);
                            $employee = get_results('users', "user_type='Employee' AND belge_no<>'' AND is_delete=0 AND isletme_id=" . ISLETME_ID);
                            ?>
                            <select class="form-select" data-control="select2" data-placeholder="Seçim yapınız" name="data[sorumlu_muh]" id="data_sorumlu_muh" required>
                                <option></option>
                                <optgroup label="Sondörler">
                                    <?php foreach ($sondorler as $k => $v): ?>
                                        <option <?php echo ($v->user_id == $data->sorumlu_muh) ? 'selected="selected"' : ''; ?> value="<?php echo $v->user_id; ?>"><?php echo $v->name . ' ' . $v->lastname; ?> - <?php echo $v->firma_adi ?></option>
                                    <?php endforeach; ?>
                                </optgroup>
                                <optgroup label="Personel">
                                    <?php foreach ($employee as $k => $v): ?>
                                        <option <?php echo ($v->user_id == $data->sorumlu_muh) ? 'selected="selected"' : ''; ?> value="<?php echo $v->user_id; ?>"><?php echo $v->name . ' ' . $v->lastname; ?></option>
                                    <?php endforeach; ?>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <?php form_input('Enlem', 'latitude', true); ?>
                    </div>
                    <div class="col-md-2">
                        <?php form_input('Boylam', 'longitude', true); ?>
                    </div>
                    <div class="col-md-2">
                        <?php form_input('Sondaj Kotu', 'sondaj_kotu'); ?>
                    </div>
                    <div class="col-md-2">
                        <?php form_input_group('Delği Çapı', 'delgi_capi', 'mm'); ?>
                    </div>

                    <div class="col-md-4">
                        <?php form_select('SPT Şahmerdan Tipi', 'sahmerdan_tipi', array('Yarı Otomatik', 'Otomatik'), false, true); ?>
                    </div>

                    <div class="col-md-4">
                        <?php form_select('Makine Tipi', 'makine_tipi', array('Rotary/Burgulu Sondaj', 'Diger Secenek'), false, true); ?>
                    </div>
                    <div class="col-md-2">
                        <?php form_input_group('Derinligi', 'sondaj_derinligi', 'm'); ?>
                    </div>
                    <?php if (!$sk_id): ?>
                        <?php form_separator('Çoğalt') ?>
                        <div class="col-md-4">
                            <?php form_input('Eklenecek SK sayısı', 'eklenecek_sk_sayisi', true, 'Yukarıdaki bilgileri kopyalarak belirltilen sayı kadar SK oluşturur.');  ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-footer border-0 d-flex justify-content-end">
                <a type="button" data-text="Bu SK ile tüm içerik bilgileri silenecektir. Onaylıyor musunuz?" href="<?php action_link('sk_sil&sk_id=' . $sk->id); ?>" class="btn btn-light-danger s_confirm  btn-evo-loading me-2">Sil</a>
                <button type="submit" class="btn btn-primary btn-evo-loading"><?php echo ($sk_id) ? 'Güncelle' : 'Ekle' ?></button>
            </div>
        </div>
    </div>
</form>
<?php
$yer_alti_sulari = get_results('yeralti_sulari', "sk_id=$data->id");
if (empty($yer_alti_sulari)) {
    $yer_alti_sulari = array();
    $yer_alti_sulari[] = array('derinlik' => 'A');
}
?>
<form class="form valid_form" method="POST">
    <input type="hidden" name="do" value="<?php echo $target; ?>/<?php echo $mode ?>" />
    <input type="hidden" name="action" value="<?php echo $action; ?>" />
    <input type="hidden" name="islem" value="derinlik" />
    <input type="hidden" name="data[sk_id]" value="<?php echo ($data->id) ? $data->id : 0; ?>" />
    <input type="hidden" name="data[rapor_id]" value="<?php echo $rapor->id; ?>" />
    <div class="card  card-flush mb-10 shadow-sm">
        <div class="card-header collapsible cursor-pointer rotate" data-bs-toggle="collapse" data-bs-target="#card_yer_alt_suyu">
            <div class="card-title pt-5 d-flex flex-column">
                <h3>Yer Altı Suyu</h3>
                <span class="text-gray-500 mt-1 fw-semibold fs-6"><?php echo $data->adi; ?></span>
            </div>
            <div class="card-toolbar rotate-180">
                <i class="ki-duotone ki-down fs-1"></i>
            </div>
        </div>
        <div id="card_yer_alt_suyu" class="collapse show">
            <?php if ($data->id): ?>
                <div class="card-body">

                    <div id="row_derinlik">
                        <!--begin::Form group-->
                        <div class="inner-repeater">
                            <div data-repeater-list="row_derinlik">
                                <?php foreach ($yer_alti_sulari as $k => $data):  ?>
                                    <div data-repeater-item>
                                        <div class="form-group row">
                                            <div class="col-md-2">
                                                <?php form_input('Derinlik', 'derinlik'); ?>
                                            </div>
                                            <div class="col-md-2">
                                                <?php //form_input('Tarih', 'tarih', '', '', 'flatpicker-evo'); 
                                                ?>
                                                <div class="mb-4 fv-row">
                                                    <label class="form-label ">Tarih</label>
                                                    <input type="text" name="data[tarih]" value="<?php echo unix_to_date($data->tarih); ?>" class="form-control data-mask">

                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <?php form_input('Açıklama', 'aciklama'); ?>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="javascript:;" data-repeater-delete class="btn btn-sm btn-light-danger mt-3 mt-md-8">
                                                    <i class="ki-duotone ki-trash fs-5"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span></i>
                                                    Sil
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <!--end::Form group-->

                        <!--begin::Form group-->
                        <div class="form-group mt-5">
                            <a href="javascript:;" data-repeater-create class="btn btn-light-primary">
                                <i class="ki-duotone ki-plus fs-3"></i>
                                Ekle
                            </a>
                        </div>
                        <!--end::Form group-->
                    </div>


                </div>
                <div class="card-footer border-0 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary btn-evo-loading"><?php echo ($sk_id) ? 'Güncelle' : 'Ekle' ?></button>
                </div>
            <?php else: ?>
                <div class="d-flex flex-center border-dashed border-1 text-gray-700 m-10 p-10">Yer altı su bilgisi için öncelikle Sondaj bilgisini kaydediniz.</div>
            <?php endif; ?>
            <!--end::Repeater-->
        </div>
    </div>
</form>