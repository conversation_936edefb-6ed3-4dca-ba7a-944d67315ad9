<?php
$sk = get_row('sklar', "id=$sk_id AND isletme_id=" . ISLETME_ID);
if ($sk):
    $logs = get_results('log', "rapor_id=$id AND sk_id=$sk_id AND isletme_id=" . ISLETME_ID . " order by position");
?>

    <div class="card card-flush mb-10">
        <div class="card-header">
            <div class="card-title">
                <h3 class="text-gray-700">Log</h3>
            </div>
        </div>
        <div class="card-body p-5 pt-0">
            <?php
            if ($logs):
                foreach ($logs as $k => $log): ?>
                    <div class="d-flex bg-hover-gray-100 px-2 py-0" id="row-<?php echo $log->id ?>">
                        <div class="d-flex align-items-center me-4">
                            <button class="btn btn-icon btn-sm  btn-light-danger mt-4 w-20px h-20px rounded btn_remove_log" data-id="<?php echo $log->id; ?>">
                                <i class="ki-outline ki-cross fs-6"></i>
                            </button>
                        </div>
                        <?php
                        include 'ornek_turu/common.php';
                        if ($log->ornek_turu == 'spt') include 'ornek_turu/spt.php';
                        include 'ornek_turu/presiyometre.php';
                        if ($log->ornek_turu == 'karot') include 'ornek_turu/karot.php';
                        ?>
                    </div>
                <?php endforeach;
            else:
                ?>
                <div class="text-center pb-10">
                    <div class=" fw-semibold text-gray-700">
                        Kayıtlı log bulunamadı.
                    </div>

                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php

    if ($logs) {
        $derinlik_baslangic = $log->derinlik_baslangic + 1.5;
        $derinlik_bitis = $derinlik_baslangic + 0.5;
    } else {
        $derinlik_baslangic = 1;
        $derinlik_bitis = 1.5;
    }
    ?>
    <form class="card  card-flush mt-10 shadow-sm form valid_form" method="POST">
        <input type="hidden" name="do" value="<?php echo $target; ?>/<?php echo $mode ?>" />
        <input type="hidden" name="action" value="<?php echo $action; ?>" />
        <input type="hidden" name="islem" value="yeni_spt" />
        <input type="hidden" name="sk_id" value="<?php echo ($sk->id) ? $sk->id : 0; ?>" />
        <input type="hidden" name="rapor_id" value="<?php echo $rapor->id; ?>" />

        <a id="yeni_ekle"></a>
        <div class="card-header collapsible cursor-pointer rotate collapsed " data-bs-toggle="collapse" data-bs-target="#yeni_veri_analizi_ekle">
            <div class="card-title ">
                <h5>Yeni Ekle</h5>
            </div>
            <div class="card-toolbar rotate-180">
                <i class="ki-duotone ki-down fs-1"></i>
            </div>
        </div>
        <div id="yeni_veri_analizi_ekle" class="collapse show">
            <div class="card-body table-responsive">

                <div class="mb-4 d-flex">
                    <div class="d-flex flex-column">
                        <h6 class="text-gray-500 mb-4 fs-sm">Derinlik</h6>
                        <div class="d-flex">
                            <div class="w-70px mb-4 me-2">
                                <label class=" form-label ">Başlangıç:</label>
                                <input type="number" step=".5" min="0.0" max="100.5" name="derinlik_baslangic" id="derinlik_baslangic" class="form-control mb-2 mb-md-0 ps-2" value="<?php echo $derinlik_baslangic; ?>" placeholder="" />
                            </div>
                            <div class="w-70px mb-4 me-2">
                                <label class=" form-label ">Bitiş:</label>
                                <input type="number" step=".5" min="0.0" max="100.5" name="derinlik_bitis" id="derinlik_bitis" class="form-control mb-2 mb-md-0 ps-2" value="<?php echo $derinlik_bitis; ?>" placeholder="" />
                            </div>
                        </div>

                    </div>
                    <div class="d-flex flex-column me-2">
                        <h6 class="text-gray-500 mb-4 fs-sm">&nbsp;</h6>
                        <div class="d-flex">
                            <div class="w-125px mb-4 me-4">
                                <label class=" form-label ">Örnek Türü:</label>
                                <select class="form-select" id="ornek_turu" data-control="select2" data-hide-search="true" data-placeholder="Seçim yapınız" name="ornek_turu" required>
                                    <option></option>
                                    <option value="spt" selected>SPT</option>
                                    <option value="ud">UD</option>
                                    <option value="karot">Karot</option>
                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="d-flex flex-column me-4  spt_row">
                        <h6 class="text-gray-500 mb-4 fs-sm">Standart Penetrasyon Testi(SPT)</h6>
                        <div class="d-flex">
                            <div class="w-100px  mb-4 me-2">
                                <label class="form-label">0-15:</label>
                                <input type="text" name="0_15" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-100px  mb-4 me-2">
                                <label class="form-label">15-30:</label>
                                <input type="text" name="15_30" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-100px  mb-4 me-2">
                                <label class="form-label">30-45:</label>
                                <input type="text" name="30_45" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-100px  mb-4 me-2">
                                <label class="form-label">N</label>
                                <input type="text" name="n" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                        </div>
                    </div>
                    <div class="d-flex flex-column d-none presiyometre_row me-4">
                        <h6 class="text-gray-500 mb-4 fs-sm">Presiyometre Deneyi</h6>
                        <div class="d-flex">
                            <div class="w-100px  mb-4 me-2">
                                <label class="form-label">Elastisite M.</label>
                                <input type="text" name="elastisite" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-100px  mb-4 me-2">
                                <label class="form-label">Net Limit B.</label>
                                <input type="text" name="limit_basinc" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                        </div>
                    </div>
                    <div class="d-flex flex-column d-none karot_row">
                        <h6 class="text-gray-500 mb-4 fs-sm">Kaya Özellikleri</h6>
                        <div class="d-flex">
                            <div class="w-90px  mb-4 me-2">
                                <label class="form-label">TCR%</label>
                                <input type="text" name="tcr" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-90px  mb-4 me-2">
                                <label class="form-label">SCR%</label>
                                <input type="text" name="scr" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-90px  mb-4 me-2">
                                <label class="form-label">RQD%</label>
                                <input type="text" name="rqd" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-90px  mb-4 me-2">
                                <label class="form-label">Ayrışma D.</label>
                                <input type="text" name="ayrisma_derecesi" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-90px  mb-4 me-2">
                                <label class="form-label">Çatlak S.</label>
                                <input type="text" name="catlak_sikligi" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                            <div class="w-90px  mb-4 me-2">
                                <label class="form-label">Dayanım</label>
                                <input type="text" name="dayanim" class="form-control mb-2 mb-md-0" value="" placeholder="" />
                            </div>
                        </div>
                    </div>

                </div>
                <?php
                /* form_separator('Çoğalt');

            $data = new stdClass();
            $data->eklenecek_sk_sayisi = 1;
          
            <div class="col-md-3">
                <?php form_input('Eklenecek SK sayısı', 'eklenecek_sk_sayisi', true, 'Yukarıdaki bilgileri çoğaltma adedi.');  ?>
            </div>
            <?php */ ?>
            </div>
            <div class="card-footer border-0 d-flex justify-content-end">
                <button type="submit" class="btn btn-primary btn-evo-loading">Ekle</button>
            </div>
        </div>
    </form>
<?php else: ?>
    Sk Bulunamadi
<?php endif; ?>