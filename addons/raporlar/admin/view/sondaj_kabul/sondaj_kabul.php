<?php

if (isset($part)) {
    $content = get_row('sondaj_kabul', "part=$part AND rapor_id=" . $rapor->id);
    if (!$content) {

        if ($rapor->ilce == 'Nilüfer') {
            $content = render_sondaj_kabul_nilufer($rapor->id, $part);
        } else {
            $content =  render_sondaj_kabul_osmangazi($rapor->id, $part);
        }

        // Otomatik kayıt oluştur
        $_data = array();
        $_data['rapor_id'] = $rapor->id;
        $_data['content'] = $content;
        $_data['part'] = $part;
        $_data['ilce'] = $rapor->ilce;
        $content_id = fn_insert_array('sondaj_kabul', $_data);
        fn_set_notice('Otomatik içerik oluşturuldu.');
        fn_redirect(action_link('1&part=' . $part, 1));
    }
}

?><div class="row">

    <div class="col-md-3">
        <?php include 'inc/sidebar.php'; ?>
    </div>
    <div class="col-md-9">
        <div class="card card-flush  mb-10">
            <div class="card-body p-0 m-0">
                <?php
                if ($islem == 'kroki'):
                    include 'inc/kroki.php';
                else:
                    if ($content):
                        if ($rapor->ilce == 'Nilüfer') {
                            include 'inc/content_nilufer.php';
                        } else {
                            // echo  render_sondaj_kabul_osmangazi($rapor->id, $part);
                            include 'inc/content_osmangazi.php';
                        }
                    else:
                ?>
                        <div class="alert alert-dismissible  bg-light d-flex flex-center flex-column py-10 px-10 px-lg-20 ">
                            <div class="text-center mt-20">
                                <div class="mb-9 fw-semibold text-gray-700">Lütfen sol taraftaki menüden bir <strong>seçim yapınız</strong>.</div>
                            </div>
                        </div>
                <?php endif;
                endif; ?>
            </div>
        </div>
    </div>

</div>
<script>
    var uploadEditorUrl = '';
</script>