<?php
$sklar = get_results('sklar', "rapor_id=$rapor->id AND isletme_id=" . ISLETME_ID);

?>
<div class="card">
    <div class="card-body ">
        <a href="<?php action_link('kroki') ?>" class="d-flex align-items-center mb-1 <?php echo ($islem == 'kroki') ? 'active' : ''; ?> text-gray-900 justify-content-between text-hover-primary text-active-primary">
            <span class="text-active-primary fs-6 fw-bold mb-2">Kroki</span>
            <i class="ki-outline ki-picture fs-5"></i>
        </a>
        <div class="separator mb-3 opacity-75"></div>
        <?php if ($sklar):
            foreach ($sklar as $k => $v):

                $start = $i * 6;
                $end = $start + 6;
        ?>
                <div class="d-flex mb-3 align-items-center justify-content-between">
                    <a href="<?php action_link('1&part=' . $v->id) ?>" class="fs-5 fw-bold <?php echo (isset($part) && $v->id == $part) ? 'active' : ''; ?> text-gray-900 justify-content-between text-hover-primary text-active-primary">
                        <?php echo $v->adi; ?>
                    </a>
                    <a href="<?php action_link('delete_content&part=' . $v->id) ?>" data-text="Bu içerik silinecek ve yeniden oluşturulacak." class="d-flex s_confirm mb-2 align-items-center <?php echo (isset($part) && $v->id == $part) ? 'active' : ''; ?> text-gray-900 justify-content-between text-hover-primary text-active-primary">
                        <i class="ki-outline ki-trash fs-5"></i>
                    </a>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="p-4">
                <div class="text-center">
                    <div class="fw-semibold text-muted">Kayıtlı içerik bulunamadı</div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>