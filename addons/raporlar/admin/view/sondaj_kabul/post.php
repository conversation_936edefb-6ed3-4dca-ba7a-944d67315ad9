<?php
if ($islem == 'delete_content') {

    fn_delete_row('sondaj_kabul', "rapor_id=$id AND part=$part");
    fn_set_notice('İçerik silindi', 'W');
    fn_redirect(action_link('1&part=' . $part, 1));
}
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($islem == 'upload_kroki') {
        $images_dir = images_url($target) .  'kroki' . DS;
        $_data = array();
        $_data['sondaj_kroki'] = fn_file_upload($_FILES['kroki'], $images_dir);
        fn_update_array('raporlar', $_data, "id=$id");
        if ($before_avatar) {
            fn_delete_file($images_dir . $before_avatar);
        }

        fn_set_notice('Kroki başarıyla yüklendi.');
        fn_redirect(action_link('sondaj_kabul', 1));
    }
}
