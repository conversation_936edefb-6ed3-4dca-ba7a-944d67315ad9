<?php
if ($category_id):
    $data = fn_get_category_row($category_id);
    $data->id = $data->category_id;
    $parent_id = $data->parent_id;

    // Rapor ID'sini al ve değişkenleri işle
    $current_rapor_id = $data->rapor_id ? $data->rapor_id : get_current_rapor_id();

    // İçerikteki değişkenleri işle (sadece görüntüleme için, form verisi olduğu gibi kalacak)
    $processed_description = process_rapor_variables($data->description, $current_rapor_id);
    echo $processed_description;
    die;

?><form class="form-horizontal valid_form" method="post" enctype="multipart/form-data">
        <input type="hidden" name="target" value="<?php echo $target . "." . $mode ?>" />
        <input type="hidden" name="mode" value="<?php echo $mode ?>">
        <input type="hidden" name="islem" value="<?php echo $islem ?>">
        <input type="hidden" name="category_id" value="<?php echo $data->category_id ?>">
        <div class="card card-custom">
            <div class="card-header py-0 align-items-center">
                <div class="card-title align-items-start flex-column">
                    <h5 class="">Sayfa Düzenle: <?php echo $data->category; ?></h5>
                </div>
                <div class="card-toolbar"></div>
            </div>
            <div class="card-body">
                <div class="mb-6 row">
                    <label class="col-lg-2 form-label required"><?php echo _('Bağlı Olduğu Sayfa'); ?></label>
                    <div class="col-lg-10 ">
                        <?php
                        fn_category_com('rapor_doc', 'data[parent_id]', $parent_id, 'class="form-select" data-control="select2" ', $rapor->id); ?>
                    </div>
                </div>
                <div class="mb-6 row">
                    <label class="col-lg-2 form-label required">Sayfa Adı</label>
                    <div class="col-lg-10">
                        <input type="text" name="data[category]" class="form-control" value="<?php echo $data->category; ?>" placeholder="" required />
                    </div>
                </div>
                <div class="mb-6 row">
                    <label class="col-lg-2 form-label"><?php echo _('Açıklama'); ?></label>
                    <div class="col-lg-10 ">
                        <!-- Değişken Yardım Paneli -->
                        <div class="mb-3">
                            <button type="button" class="btn btn-sm btn-light-primary" data-bs-toggle="collapse" data-bs-target="#variablesPanel" aria-expanded="false">
                                <i class="fas fa-code"></i> Değişkenler
                            </button>
                        </div>

                        <!-- Değişken Listesi Paneli -->
                        <div class="collapse mb-3" id="variablesPanel">
                            <div class="card card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <?php echo render_variables_list(); ?>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Değişkenleri kullanmak için yukarıdaki kodları kopyalayıp editöre yapıştırabilirsiniz.
                                        Değişkenler rapor görüntülenirken otomatik olarak gerçek değerlerle değiştirilecektir.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Gizli textarea form verisi için (orijinal veri) -->
                        <textarea class="d-none" name="data[description]" id="description_hidden"><?php echo @$data->description; ?></textarea>
                        <!-- DecoupledEditor için div elementi - Benzersiz ID ve sınıf (işlenmiş veri gösterimi) -->
                        <div class="rapor-doc-editor-unique border-bottom " id="description"><?php echo @$data->description; ?></div>

                        <!-- Değişken önizleme paneli -->
                        <?php if ($current_rapor_id && $processed_description != $data->description): ?>
                            <div class="mt-3">
                                <button type="button" class="btn btn-sm btn-light-info" data-bs-toggle="collapse" data-bs-target="#previewPanel" aria-expanded="false">
                                    <i class="fas fa-eye"></i> Değişkenler İşlenmiş Önizleme
                                </button>
                            </div>

                            <div class="collapse mt-3" id="previewPanel">
                                <div class="card card-body bg-light">
                                    <h6 class="text-muted mb-3">Değişkenler İşlenmiş Hali:</h6>
                                    <div class="border rounded p-3 bg-white">
                                        <?php echo $processed_description; ?>
                                    </div>
                                    <small class="text-muted mt-2">
                                        <i class="fas fa-info-circle"></i>
                                        Bu önizleme, değişkenlerin nasıl görüneceğini gösterir. Editördeki orijinal metin değişmez.
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php include  COMMON_DIR . '/card/footer.php';  ?>
        </div>
    </form>
    <?php
    // CKEditor Document için gerekli JavaScript ve CSS dosyalarını ekle
    $page_js[] = 'theme/admin/jeotek/assets/plugins/custom/ckeditor/ckeditor-document.bundle.js';

    // Değişken sistemi için CSS dosyasını ekle
    $page_css[] = 'addons/raporlar/css/raporlar_variables.css';
    ?>
<?php else: ?>
<?php endif; ?>