<?php
if ($category_id):
    $data = fn_get_category_row($category_id);
    $data->id = $data->category_id;
    $parent_id = $data->parent_id;

?><form class="form-horizontal valid_form" method="post" enctype="multipart/form-data">
        <input type="hidden" name="target" value="<?php echo $target . "." . $mode ?>" />
        <input type="hidden" name="mode" value="<?php echo $mode ?>">
        <input type="hidden" name="islem" value="<?php echo $islem ?>">
        <input type="hidden" name="category_id" value="<?php echo $data->category_id ?>">
        <div class="card card-custom">
            <div class="card-header py-0 align-items-center">
                <div class="card-title align-items-start flex-column">
                    <h5 class="">Sayfa Düzenle: <?php echo $data->category; ?></h5>
                </div>
                <div class="card-toolbar"></div>
            </div>
            <div class="card-body">
                <div class="mb-6 row">
                    <label class="col-lg-2 form-label required"><?php echo _('Bağlı Olduğu Sayfa'); ?></label>
                    <div class="col-lg-10 ">
                        <?php
                        fn_category_com('rapor_doc', 'data[parent_id]', $parent_id, 'class="form-select" data-control="select2" ', $rapor->id); ?>
                    </div>
                </div>
                <div class="mb-6 row">
                    <label class="col-lg-2 form-label required">Sayfa Adı</label>
                    <div class="col-lg-10">
                        <input type="text" name="data[category]" class="form-control" value="<?php echo $data->category; ?>" placeholder="" required />
                    </div>
                </div>
                <div class="mb-6 row">
                    <label class="col-lg-2 form-label"><?php echo _('Açıklama'); ?></label>
                    <div class="col-lg-10 ">
                        <!-- Gizli textarea form verisi için -->
                        <textarea class="d-none" name="data[description]" id="description_hidden"><?php echo @$data->description; ?></textarea>
                        <!-- DecoupledEditor için div elementi - Benzersiz ID ve sınıf -->
                        <div class="rapor-doc-editor-unique border-bottom " id="description"><?php echo @$data->description; ?></div>
                    </div>
                </div>
            </div>
            <?php include  COMMON_DIR . '/card/footer.php';  ?>
        </div>
    </form>
    <?php
    // CKEditor Document için gerekli JavaScript ve CSS dosyalarını ekle
    $page_js[] = 'theme/admin/jeotek/assets/plugins/custom/ckeditor/ckeditor-document.bundle.js';
    ?>
<?php else: ?>
<?php endif; ?>