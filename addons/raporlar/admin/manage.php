<?php
$data = $db->get_results("SELECT * FROM $target WHERE is_delete=0");
?>
<div class="card card-flush">
    <?php include  COMMON_DIR . '/card/header.php'; ?>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table align-middle table-row-dashed fs-6 gy-5 " id="custom_data_table">
                <thead>
                    <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                        <th><?php echo _('Proje'); ?></th>
                        <th><?php echo _('Sorumlu Müh.'); ?></th>
                        <th><?php echo _('Adres'); ?></th>
                        <th><?php echo _('Kuyu Listesi'); ?></th>
                        <th><?php echo _('Durum'); ?></th>
                        <th></th>
                    </tr>
                </thead>
                <tbody class="fw-bold text-gray-600">
                    <?php if ($data) : ?>
                        <?php
                        foreach ($data as $key => $v) :
                            $musteri = get_row('users', "user_id=$v->customer_id");
                            $sorumlu_muh_ids = array_map(
                                function ($v) {
                                    return $v['sorumlu_muh'];
                                },
                                get_results('sklar', "rapor_id=$v->id GROUP BY sorumlu_muh", 'sorumlu_muh', ARRAY_A)
                            );
                            if ($sorumlu_muh_ids)
                                $muhendisler = get_results('users', "user_id IN (" . implode(',', $sorumlu_muh_ids) . ")");
                            else
                                $muhendisler = array();
                            $spk_listesi = get_results('sklar', "rapor_id=$v->id", 'adi,id');

                        ?>
                            <tr id="row-<?php echo $v->id ?>">
                                <td data-sort="<?php echo $v->id ?>">
                                    <div class="d-flex justify-content-start flex-column">
                                        <a href="<?php fn_menu_link($target . '/view&id=' . $v->id) ?>" class="text-gray-800 fw-bold text-hover-primary mb-1 fs-6"><?php echo $v->proje_adi; ?></a>
                                        <span class="text-gray-500 fw-semibold d-block fs-7"><?php echo $musteri->name . ' ' . $musteri->lastname; ?> (<?php echo $musteri->firma_adi; ?>)</span>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($muhendisler):  ?>
                                        <div class="symbol-group symbol-hover ">
                                            <?php foreach ($muhendisler as $a => $b):
                                                $image_url = ($b->user_type == 'Provider') ? images_url('sondorler', 'url') : images_url('employee', 'url');
                                            ?>

                                                <div class="symbol symbol-35px symbol-circle" data-bs-toggle="tooltip" title="<?php echo $b->name . ' ' . $b->lastname; ?>">
                                                    <?php if ($b->avatar): ?>
                                                        <img alt="Pic" src="<?php echo T . $image_url . $b->avatar . '&w=120' ?>" />
                                                    <?php else: ?>
                                                        <span class="symbol-label bg-warning text-inverse-warning fw-bold"><?php echo $b->name[0] ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-start flex-column">
                                        <div class="text-gray-800 fw-bold"><?php echo $v->il; ?> <?php echo $v->ilce; ?> <?php echo $v->mahalle; ?></div>
                                        <div>
                                            <span>Ada: <?php echo $v->ada; ?></span>
                                            <span>Pafta: <?php echo $v->pafta; ?></span>
                                            <span>Parsel: <?php echo $v->parsel; ?></span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($spk_listesi): ?>
                                        <?php foreach ($spk_listesi as $g => $s): ?>
                                            <a href="<?php fn_menu_link($target . '/view&id=' . $v->id . '&action=sk&islem=spt&sk_id=' . $s->id) ?>" class="badge badge-light"><?php echo $s->adi; ?></a>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <select data-qucikupdate="raporlar,durum" data-quick-id="id,<?php echo $v->id; ?>" class="form-select form-select-sm" data-control="select2" data-hide-search="true" data-placeholder="Seçim yapınız">
                                        <option <?php echo ($v->durum == 'bitti') ? 'selected="selected"' : ''; ?> value="bitti">Bitti</option>
                                        <option <?php echo ($v->durum == 'devam-ediyor') ? 'selected="selected"' : ''; ?> value="devam-ediyor">Devam Ediyor</option>
                                    </select>
                                </td>
                                <td nowrap="nowrap" class="text-end">
                                    <a class="btn btn-sm btn-icon btn-secondary  me-1" href="<?php fn_menu_link($target . '/view&id=' . $v->id) ?>"><i class="ki-duotone ki-element-11 fs-3"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span></i></a>
                                    <?php table_action_row($v); ?>
                                </td>
                            </tr>
                        <?php endforeach ?>
                    <?php endif ?>
                </tbody>
            </table>
        </div>
    </div>
</div>