<?php
/**
 * Düzenlenebilir Değişkenler Yönetim Sayfası
 */

// Yetki kontrolü
if (!USER_LOGIN) {
    fn_set_notice('Yet<PERSON>z yok!', 'E');
    fn_redirect('?do=home');
}

// POST işlemleri
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['save_variables'])) {
    $saved_count = 0;
    $rapor_id = isset($_POST['rapor_id']) ? intval($_POST['rapor_id']) : 0;
    
    foreach ($_POST['variables'] as $variable_name => $value) {
        if (save_editable_variable_value($variable_name, $value, $rapor_id)) {
            $saved_count++;
        }
    }
    
    if ($saved_count > 0) {
        fn_set_notice($saved_count . ' değişken başarıyla kaydedildi.');
    } else {
        fn_set_notice('Değişkenler kaydedilemedi.', 'W');
    }
    
    // Aynı sayfaya yönlendir
    $redirect_url = '?do=raporlar/variables_edit';
    if ($rapor_id > 0) {
        $redirect_url .= '&rapor_id=' . $rapor_id;
    }
    fn_redirect($redirect_url);
}

// Rapor ID'sini al
$rapor_id = isset($_GET['rapor_id']) ? intval($_GET['rapor_id']) : 0;

// Değişken tanımlamaları dosyasını dahil et
require_once ADDONS_DIR . '/raporlar/config/variables.php';
global $rapor_variables;

// Sadece düzenlenebilir değişkenleri filtrele
$editable_variables = [];
foreach ($rapor_variables as $name => $variable) {
    if (isset($variable['editable']) && $variable['editable'] === true) {
        $editable_variables[$name] = $variable;
    }
}

// Rapor bilgisini al (eğer rapor ID'si varsa)
$rapor = null;
if ($rapor_id > 0) {
    $rapor = get_row('raporlar', "id=$rapor_id AND isletme_id=" . ISLETME_ID);
    if (!$rapor) {
        fn_set_notice('Rapor bulunamadı.', 'W');
        fn_redirect('?do=raporlar/manage');
    }
}

// Tüm raporları listele (dropdown için)
$raporlar = get_results('raporlar', "isletme_id=" . ISLETME_ID . " ORDER BY created_date DESC LIMIT 50");
?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <?php if ($rapor_id > 0): ?>
                Rapor Özel Değişkenleri: <?php echo htmlspecialchars($rapor->baslik); ?>
            <?php else: ?>
                Global Değişkenler
            <?php endif; ?>
        </h3>
        <div class="card-toolbar">
            <div class="d-flex align-items-center">
                <!-- Rapor Seçici -->
                <select class="form-select form-select-sm me-3" id="rapor-selector" style="width: 300px;">
                    <option value="0" <?php echo $rapor_id == 0 ? 'selected' : ''; ?>>Global Değişkenler</option>
                    <?php if ($raporlar): ?>
                        <?php foreach ($raporlar as $r): ?>
                            <option value="<?php echo $r->id; ?>" <?php echo $rapor_id == $r->id ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($r->baslik); ?> (ID: <?php echo $r->id; ?>)
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
                
                <a href="?do=raporlar/view&action=rapor&id=<?php echo $rapor_id; ?>" class="btn btn-sm btn-light">
                    <i class="fas fa-arrow-left"></i> Geri
                </a>
            </div>
        </div>
    </div>
    
    <div class="card-body">
        <?php if (empty($editable_variables)): ?>
            <div class="alert alert-info">
                Henüz düzenlenebilir değişken tanımlanmamış.
            </div>
        <?php else: ?>
            <form method="post" class="form">
                <input type="hidden" name="rapor_id" value="<?php echo $rapor_id; ?>">
                
                <div class="row">
                    <?php foreach ($editable_variables as $name => $variable): ?>
                        <?php
                        // Mevcut değeri al
                        $current_value = get_editable_variable_value($name, $variable['value'], $rapor_id);
                        ?>
                        <div class="col-12 mb-6">
                            <div class="card card-bordered">
                                <div class="card-header">
                                    <h4 class="card-title">{<?php echo $name; ?>}</h4>
                                    <div class="card-toolbar">
                                        <span class="badge badge-light-primary"><?php echo $variable['category']; ?></span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label"><?php echo $variable['label']; ?></label>
                                        <p class="text-muted small"><?php echo $variable['description']; ?></p>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <textarea 
                                            name="variables[<?php echo $name; ?>]" 
                                            class="form-control variable-editor" 
                                            rows="4"
                                            placeholder="<?php echo htmlspecialchars($variable['value']); ?>"
                                        ><?php echo htmlspecialchars($current_value); ?></textarea>
                                    </div>
                                    
                                    <?php if ($current_value != $variable['value']): ?>
                                        <div class="alert alert-warning">
                                            <strong>Özelleştirilmiş:</strong> Bu değişken varsayılan değerinden farklı.
                                            <br><small><strong>Varsayılan:</strong> <?php echo htmlspecialchars($variable['value']); ?></small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-sm btn-light-info preview-btn" data-variable="<?php echo $name; ?>">
                                            <i class="fas fa-eye"></i> Önizleme
                                        </button>
                                        <button type="button" class="btn btn-sm btn-light-warning reset-btn" data-variable="<?php echo $name; ?>" data-default="<?php echo htmlspecialchars($variable['value']); ?>">
                                            <i class="fas fa-undo"></i> Varsayılana Dön
                                        </button>
                                    </div>
                                    
                                    <!-- Önizleme alanı -->
                                    <div class="preview-area mt-3" id="preview-<?php echo $name; ?>" style="display: none;">
                                        <div class="card card-body bg-light">
                                            <h6>Önizleme:</h6>
                                            <div class="preview-content"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="d-flex justify-content-end mt-6">
                    <button type="submit" name="save_variables" class="btn btn-primary">
                        <i class="fas fa-save"></i> Değişkenleri Kaydet
                    </button>
                </div>
            </form>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Rapor seçici
    document.getElementById('rapor-selector').addEventListener('change', function() {
        const raporId = this.value;
        const url = raporId == 0 ? '?do=raporlar/variables_edit' : '?do=raporlar/variables_edit&rapor_id=' + raporId;
        window.location.href = url;
    });
    
    // Önizleme butonları
    document.querySelectorAll('.preview-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const variableName = this.dataset.variable;
            const textarea = document.querySelector(`textarea[name="variables[${variableName}]"]`);
            const previewArea = document.getElementById(`preview-${variableName}`);
            const previewContent = previewArea.querySelector('.preview-content');
            
            if (previewArea.style.display === 'none') {
                // Önizlemeyi göster
                const content = textarea.value || textarea.placeholder;
                previewContent.innerHTML = content;
                previewArea.style.display = 'block';
                this.innerHTML = '<i class="fas fa-eye-slash"></i> Önizlemeyi Gizle';
            } else {
                // Önizlemeyi gizle
                previewArea.style.display = 'none';
                this.innerHTML = '<i class="fas fa-eye"></i> Önizleme';
            }
        });
    });
    
    // Varsayılana dön butonları
    document.querySelectorAll('.reset-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const variableName = this.dataset.variable;
            const defaultValue = this.dataset.default;
            const textarea = document.querySelector(`textarea[name="variables[${variableName}]"]`);
            
            if (confirm('Bu değişkeni varsayılan değerine döndürmek istediğinizden emin misiniz?')) {
                textarea.value = defaultValue;
            }
        });
    });
});
</script>

<?php
// CSS dosyasını ekle
$page_css[] = 'addons/raporlar/css/raporlar_variables.css';
?>
