<?php

if ($_SERVER['REQUEST_METHOD'] == 'POST') {



    if ($_FILES) {

        foreach ($_FILES as $k => $v) {
            if ($_FILES[$k]['name']) {
                $images_dir = ${$target . "_images"};
                $data[$k] = fn_file_upload($_FILES[$k], $images_dir);
            }
        }
    }
    if ($mode == 'add') {
        $id = fn_insert_array($target, $data);
        fn_set_notice("Bilgiler eklendi.");
    }

    if ($mode == 'update') {
        fn_update_array($target, $data, "id=$id limit 1");
        fn_set_notice("Bilgiler Güncellendi.");
    }
    if (USER_LOGIN == $id)
        fn_login_after($id);
    fn_redirect_submit("?do=$target/manage");
}



if ($mode == 'update' && $id) {

    $data = get_row($target, "id=$id");
} else {
    $data = new stdClass();
    $data->il_id = '16';
    $data->ilce = 'Nilüfer';
}

?>
<form class="form valid_form" method="POST" enctype="multipart/form-data">
    <input type="hidden" name="mode" value="<?php echo $mode ?>">
    <?php if (isset($data) && $data->id) : ?>
        <input type="hidden" name="id" value="<?php echo $data->id ?>">
    <?php endif ?>
    <div class="card card-custom">
        <?php include  COMMON_DIR . '/card/title.php'; ?>
        <div class="card-body">
            <div class="row">

                <div class="col-md-3">
                    <?php form_select_users('Müşteri', 'customer_id', "user_type='Customer' and is_delete=0 AND isletme_id=" . ISLETME_ID, true); ?>
                </div>
                <div class="col-md-6">
                    <?php form_input('Proje Adı', 'proje_adi', true) ?>
                </div>
            </div>
            <?php form_separator('Adres Bilgisi', '', false) ?>
            <div class="row">
                <div class="col-md-3">
                    <?php form_iller('İl', 'il_id', true); ?>
                </div>
                <div class="col-md-3">
                    <?php form_ilceler('İlçe', 'ilce', true); ?>
                </div>
                <div class="col-md-3">
                    <?php form_input('Mahalle', 'mahalle', true); ?>
                </div>
                <div class="col-md-3">
                    <?php form_input('Pafta', 'pafta', true); ?>
                </div>

                <div class="col-md-3">
                    <?php form_input('Ada', 'ada', true); ?>
                </div>
                <div class="col-md-3">
                    <?php form_input('Parsel', 'parsel', true); ?>
                </div>
                <div class="col-md-3">
                    <?php form_input('Enlem', 'latitude', true); ?>
                </div>
                <div class="col-md-3">
                    <?php form_input('Boylam', 'longitude', true); ?>
                </div>
            </div>
        </div>
        <?php include  COMMON_DIR . '/card/footer.php';  ?>
    </div>
</form>