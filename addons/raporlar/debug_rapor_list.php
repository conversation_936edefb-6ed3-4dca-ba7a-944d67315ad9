<?php
/**
 * Rapor Listesi Debug Sayfası
 */

// Temel tanımlamalar
define('AREA', 'A');
define('USER_LOGIN', 1);
define('ISLETME_ID', 1);

// Ana dizini belirle
$root_dir = dirname(dirname(__DIR__));
require_once $root_dir . '/init.php';

echo '<h1>Rapor ve Kategori Listesi</h1>';

// Raporları listele
echo '<h2>Mevcut Raporlar:</h2>';
$raporlar = get_results('raporlar', "1=1 LIMIT 10");
if ($raporlar) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>Başlık</th><th>Müşteri ID</th><th>Oluşturma Tarihi</th></tr>';
    foreach ($raporlar as $rapor) {
        echo '<tr>';
        echo '<td>' . $rapor->id . '</td>';
        echo '<td>' . htmlspecialchars($rapor->baslik) . '</td>';
        echo '<td>' . $rapor->customer_id . '</td>';
        echo '<td>' . date('d.m.Y H:i', $rapor->created_date) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>Rapor bulunamadı.</p>';
}

// Kategorileri listele
echo '<h2>Mevcut Kategoriler (rapor_doc):</h2>';
$categories = get_results('category', "object_type='rapor_doc' LIMIT 20");
if ($categories) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>Category ID</th><th>Rapor ID</th><th>Kategori Adı</th><th>Parent ID</th><th>Description (İlk 100 karakter)</th></tr>';
    foreach ($categories as $cat) {
        echo '<tr>';
        echo '<td>' . $cat->category_id . '</td>';
        echo '<td>' . $cat->rapor_id . '</td>';
        echo '<td>' . htmlspecialchars($cat->category) . '</td>';
        echo '<td>' . $cat->parent_id . '</td>';
        echo '<td>' . htmlspecialchars(substr($cat->description, 0, 100)) . '...</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p>Kategori bulunamadı.</p>';
}

// Test linklerini oluştur
echo '<h2>Test Linkleri:</h2>';
if ($categories) {
    echo '<ul>';
    foreach (array_slice($categories, 0, 5) as $cat) {
        $link = WEB_DIR . '/index.php?do=raporlar/view&action=rapor&id=' . $cat->rapor_id . '&category_id=' . $cat->category_id;
        echo '<li><a href="' . $link . '" target="_blank">Kategori: ' . htmlspecialchars($cat->category) . ' (Rapor ID: ' . $cat->rapor_id . ')</a></li>';
    }
    echo '</ul>';
}

echo '<p><a href="javascript:history.back()">← Geri Dön</a></p>';
?>
