<?php
/**
 * AJAX Değişken Değeri Alma Endpoint'i
 */

// Yetki kontrolü
if (!USER_LOGIN) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Yetkiniz yok']);
    exit;
}

// POST verilerini al
$variable_name = isset($_POST['variable_name']) ? trim($_POST['variable_name']) : '';
$rapor_id = isset($_POST['rapor_id']) ? intval($_POST['rapor_id']) : 0;

if (empty($variable_name)) {
    echo json_encode(['success' => false, 'error' => 'Değişken adı gerekli']);
    exit;
}

// Değişken tanımlamaları dosyasını dahil et
require_once ADDONS_DIR . '/raporlar/config/variables.php';
global $rapor_variables;

// Değişkenin düzenlenebilir olup olmadığını kontrol et
if (!isset($rapor_variables[$variable_name]) || !isset($rapor_variables[$variable_name]['editable']) || !$rapor_variables[$variable_name]['editable']) {
    echo json_encode(['success' => false, 'error' => 'Bu değişken düzenlenebilir değil']);
    exit;
}

try {
    // Mevcut değeri al
    $current_value = get_editable_variable_value($variable_name, $rapor_variables[$variable_name]['value'], $rapor_id);
    
    // Varsayılan değeri de gönder
    $default_value = $rapor_variables[$variable_name]['value'];
    
    // Değişken bilgilerini gönder
    $variable_info = [
        'name' => $variable_name,
        'label' => $rapor_variables[$variable_name]['label'],
        'description' => $rapor_variables[$variable_name]['description'],
        'category' => $rapor_variables[$variable_name]['category']
    ];
    
    echo json_encode([
        'success' => true,
        'value' => $current_value,
        'default_value' => $default_value,
        'variable_info' => $variable_info,
        'is_customized' => $current_value !== $default_value
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Değişken değeri alınamadı: ' . $e->getMessage()]);
}
?>
