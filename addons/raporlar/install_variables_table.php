<?php
/**
 * Değişken Tablosu Kurulum Dosyası
 */

// Temel tanımlamalar
define('AREA', 'A');
define('USER_LOGIN', 1);
define('ISLETME_ID', 1);

// Ana dizini belirle
$root_dir = dirname(dirname(__DIR__));
require_once $root_dir . '/init.php';

echo '<h1>Rapor Değişkenleri Tablosu Kurulumu</h1>';

// SQL dosyasını oku
$sql_file = ADDONS_DIR . '/raporlar/sql/rapor_variables.sql';
if (!file_exists($sql_file)) {
    echo '<p style="color: red;">SQL dosyası bulunamadı: ' . $sql_file . '</p>';
    exit;
}

$sql_content = file_get_contents($sql_file);
$sql_statements = explode(';', $sql_content);

echo '<h2>SQL Komutları Çalıştırılıyor...</h2>';

$success_count = 0;
$error_count = 0;

foreach ($sql_statements as $sql) {
    $sql = trim($sql);
    if (empty($sql) || strpos($sql, '--') === 0) {
        continue; // Boş satırları ve yorumları atla
    }
    
    echo '<div style="background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007bff;">';
    echo '<strong>SQL:</strong> ' . htmlspecialchars(substr($sql, 0, 100)) . '...';
    echo '</div>';
    
    $result = $db->query($sql);
    if ($result) {
        echo '<div style="color: green;">✓ Başarılı</div>';
        $success_count++;
    } else {
        echo '<div style="color: red;">✗ Hata: ' . $db->last_error . '</div>';
        $error_count++;
    }
    echo '<hr>';
}

echo '<h2>Sonuç:</h2>';
echo '<p><strong>Başarılı:</strong> ' . $success_count . '</p>';
echo '<p><strong>Hatalı:</strong> ' . $error_count . '</p>';

if ($error_count == 0) {
    echo '<div style="background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>✓ Kurulum Tamamlandı!</h3>';
    echo '<p>Rapor değişkenleri tablosu başarıyla oluşturuldu.</p>';
    echo '<p><a href="?do=raporlar/variables_edit" target="_blank">Değişkenleri Düzenle</a></p>';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;">';
    echo '<h3>⚠ Kurulum Tamamlanamadı</h3>';
    echo '<p>Bazı SQL komutları başarısız oldu. Lütfen hataları kontrol edin.</p>';
    echo '</div>';
}

// Mevcut tabloyu kontrol et
echo '<h2>Tablo Kontrolü:</h2>';
$table_check = $db->get_results("SHOW TABLES LIKE 'rapor_variables'");
if ($table_check) {
    echo '<p style="color: green;">✓ rapor_variables tablosu mevcut</p>';
    
    // Tablo yapısını göster
    $table_structure = $db->get_results("DESCRIBE rapor_variables");
    if ($table_structure) {
        echo '<h3>Tablo Yapısı:</h3>';
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>Alan</th><th>Tip</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
        foreach ($table_structure as $column) {
            echo '<tr>';
            echo '<td>' . $column->Field . '</td>';
            echo '<td>' . $column->Type . '</td>';
            echo '<td>' . $column->Null . '</td>';
            echo '<td>' . $column->Key . '</td>';
            echo '<td>' . $column->Default . '</td>';
            echo '<td>' . $column->Extra . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
    
    // Mevcut kayıtları göster
    $existing_records = $db->get_results("SELECT * FROM rapor_variables LIMIT 10");
    if ($existing_records) {
        echo '<h3>Mevcut Kayıtlar:</h3>';
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>ID</th><th>Değişken Adı</th><th>Değer (İlk 50 karakter)</th><th>Rapor ID</th><th>Oluşturma Tarihi</th></tr>';
        foreach ($existing_records as $record) {
            echo '<tr>';
            echo '<td>' . $record->id . '</td>';
            echo '<td>' . htmlspecialchars($record->variable_name) . '</td>';
            echo '<td>' . htmlspecialchars(substr($record->variable_value, 0, 50)) . '...</td>';
            echo '<td>' . $record->rapor_id . '</td>';
            echo '<td>' . $record->created_date . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<p>Henüz kayıt yok.</p>';
    }
} else {
    echo '<p style="color: red;">✗ rapor_variables tablosu bulunamadı</p>';
}

echo '<p><a href="javascript:history.back()">← Geri Dön</a></p>';
?>
