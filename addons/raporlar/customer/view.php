<?php
// Sayfa başlığı ve açıklaması
$page_title = 'Rapor Görüntüleme';
$page_description = 'Rapor içeriğini görüntüleme ve yazdırma';

// Rapor ID'sini al (URL'den veya başka bir kaynaktan)
$id = isset($_GET['id']) ? intval($_GET['id']) : 7; // Varsayılan olarak 7 kullanılıyor

// Rapor kategorilerini al
$tum_cat = get_results('category', "object_type='rapor_doc' AND rapor_id=$id");

// Kategorileri sıralı olarak al
$categories = array();
foreach ($tum_cat as $cat) {
    $categories[] = array(
        'id' => $cat->category_id,
        'parent_id' => $cat->parent_id,
        'category' => $cat->category,
        'description' => $cat->description,
        'position' => $cat->position
    );
}

// Kategorileri position'a göre sırala
usort($categories, function ($a, $b) {
    return $a['position'] <=> $b['position'];
});

// init.php ve func.php otomatik dahil ediliyor

// Rapor bilgilerini al
$rapor = get_row('raporlar', "id=$id");
$rapor_title = $rapor ? $rapor->baslik : 'Rapor Görüntüleme';

// Kategori içeriğini işleme fonksiyonu
function render_category_content($content)
{
    // Özel belirteçleri işleme kaldırıldı

    // Tablolara Metronic table-sm sınıfını ekle
    $content = preg_replace('/<table/', '<table class="table table-sm table-bordered"', $content);

    return $content;
}

// Kategorileri HTML olarak oluşturma fonksiyonu
function build_category_html($categories)
{
    $html = '';
    foreach ($categories as $cat) {
        $html .= '<div class="category-item mb-5" id="category-' . $cat['id'] . '">';

        // Başlık ve içerik bir arada kalması için özel div içine alıyoruz
        $html .= '<div class="category-page">';
        $html .= '<div class="category-header-content">';
        $html .= '<h2 class="category-title fw-bold">' . $cat['category'] . '</h2>';

        // İçeriği işle ve ekle
        if (!empty($cat['description'])) {
            $processed_content = render_category_content($cat['description']);

            // İçeriği paragraflara böl ve her paragrafı ayrı div içine al
            // Bu, sayfa sonlarında daha iyi kontrol sağlar
            $paragraphs = preg_split('/<\s*\/p\s*>\s*<\s*p[^>]*>/i', $processed_content);

            $html .= '<div class="category-content mt-3">';

            // İlk paragrafı özel sınıfla işaretle (başlıkla birlikte kalması için)
            if (count($paragraphs) > 0) {
                $first_paragraph = array_shift($paragraphs);
                // İlk paragrafı temizle ve ekle
                $first_paragraph = preg_replace('/^<\s*p[^>]*>/i', '', $first_paragraph);
                $first_paragraph = preg_replace('/<\s*\/p\s*>$/i', '', $first_paragraph);
                $html .= '<p class="first-paragraph">' . $first_paragraph . '</p>';

                // Kalan paragrafları ekle
                foreach ($paragraphs as $paragraph) {
                    // Paragrafı temizle ve ekle
                    $paragraph = preg_replace('/^<\s*p[^>]*>/i', '', $paragraph);
                    $paragraph = preg_replace('/<\s*\/p\s*>$/i', '', $paragraph);
                    $html .= '<p>' . $paragraph . '</p>';
                }
            } else {
                // Eğer paragraf bölme işlemi başarısız olursa, orijinal içeriği kullan
                $html .= $processed_content;
            }

            $html .= '</div>';
        }

        $html .= '</div>'; // category-header-content sonu
        $html .= '</div>'; // category-item sonu
        $html .= '</div>'; // category-page sonu
    }
    return $html;
}
?>

<!-- Rapor Görüntüleme Sayfası -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title"><?php echo $rapor_title; ?></h3>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-primary" id="print-button">
                <i class="bi bi-printer fs-4 me-2"></i>Yazdır
            </button>
        </div>
    </div>
    <div class="card-body pt-0" id="printable-content">
        <?php if (empty($categories)): ?>
            <div class="alert alert-info">
                Bu rapor için henüz içerik eklenmemiş.
            </div>
        <?php else: ?>
            <div class="report-content">
                <?php echo build_category_html($categories); ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Yazdırma için CSS dosyasını dahil et -->
<?php
// CSS dosyası otomatik dahil olacak (raporlar_print_customer.css)
?>

<script>
    // Yazdırma işlemi
    document.getElementById('print-button').addEventListener('click', function() {
        window.print();
    });

    // Sayfa yüklendiğinde yapılacak işlemler
    document.addEventListener('DOMContentLoaded', function() {
        // Uzun içeriklerin sayfalara bölünmesini sağlamak için
        const contentElements = document.querySelectorAll('.category-content');

        contentElements.forEach(function(element) {
            // Tüm tabloları Metronic table-sm sınıfı ile küçült
            const tables = element.querySelectorAll('table');
            tables.forEach(function(table) {
                // Metronic table-sm sınıfını ekle
                table.classList.add('table-sm');

                if (table.offsetHeight > 700) { // Yaklaşık A4 yüksekliği
                    // Tablo çok uzunsa, sayfa sonu ekle
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-wrapper';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });

            // Uzun görselleri kontrol et
            const images = element.querySelectorAll('img');
            images.forEach(function(img) {
                img.onload = function() {
                    if (img.height > 700) { // Yaklaşık A4 yüksekliği
                        img.style.maxHeight = '18cm';
                    }
                };
            });
        });
    });
</script>