/**
 * <PERSON><PERSON><PERSON> Yazdırma CSS
 * A4 standardında yazdırma için özel stiller
 */

/* Yazd<PERSON>rma için genel stiller */
@media print {
    /* <PERSON><PERSON> yapı<PERSON>ı */
    @page {
        size: A4;
        margin: 1.5cm;
    }
    
    /* Yaz<PERSON><PERSON>rma sırasında gizlenecek elementler */
    body * {
        visibility: hidden;
    }
    
    #printable-content, 
    #printable-content * {
        visibility: visible;
    }
    
    #printable-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        padding: 0;
        margin: 0;
        background: white;
    }
    
    /* Yazdırma sırasında gizlenecek butonlar */
    #print-button, 
    .card-toolbar,
    .header,
    .footer,
    .aside,
    .sidebar,
    .scrolltop,
    .toolbar,
    .drawer,
    .btn {
        display: none !important;
    }
    
    /* <PERSON>fa sonu kontrolü */
    .category-item {
        page-break-inside: avoid;
        margin-bottom: 20px;
    }
    
    /* Başlık stilleri */
    h1.category-title {
        font-size: 20pt;
        margin-bottom: 15pt;
    }
    
    h2.category-title {
        font-size: 18pt;
        margin-bottom: 12pt;
    }
    
    h3.category-title {
        font-size: 16pt;
        margin-bottom: 10pt;
    }
    
    h4.category-title, 
    h5.category-title, 
    h6.category-title {
        font-size: 14pt;
        margin-bottom: 8pt;
    }
    
    /* İçerik stilleri */
    .category-content {
        font-size: 12pt;
        line-height: 1.5;
    }
    
    /* Tablolar için stiller */
    table {
        width: 100%;
        border-collapse: collapse;
        page-break-inside: avoid;
        margin-bottom: 15pt;
    }
    
    table, th, td {
        border: 1px solid #ddd;
    }
    
    th, td {
        padding: 8px;
        text-align: left;
    }
    
    /* Resimler için maksimum genişlik */
    img {
        max-width: 100%;
        height: auto;
    }
    
    /* Sayfa sonları */
    .page-break {
        page-break-after: always;
    }
}

/* Normal görünüm için stiller */
.category-title {
    color: #181C32;
    margin-bottom: 1rem;
}

.category-content {
    font-size: 1rem;
    line-height: 1.5;
}

/* Yazdırma butonu */
#print-button {
    cursor: pointer;
}

/* Rapor içeriği */
.report-content {
    margin-top: 20px;
}

/* Kategori öğeleri */
.category-item {
    margin-bottom: 30px;
}

/* Tablolar için normal görünüm stilleri */
.category-content table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.category-content table, 
.category-content th, 
.category-content td {
    border: 1px solid #ddd;
}

.category-content th, 
.category-content td {
    padding: 8px;
    text-align: left;
}

/* Özel belirteçler için stiller */
.special-tag {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.special-tag-table {
    margin-top: 15px;
    margin-bottom: 15px;
}
