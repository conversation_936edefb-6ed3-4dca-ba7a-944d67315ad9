/**
 * <PERSON><PERSON><PERSON>or <PERSON>ken Plugin CSS
 */

/* <PERSON><PERSON><PERSON>eri */
.ck-variable {
    background-color: #e3f2fd;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #2196f3;
    color: #1976d2;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    font-weight: bold;
    display: inline-block;
    margin: 0 2px;
    white-space: nowrap;
}

/* Düzenlenebilir <PERSON> */
.ck-variable-editable {
    background-color: #fff3e0;
    border-color: #ff9800;
    color: #f57c00;
    cursor: pointer;
    transition: all 0.2s ease;
}

.ck-variable-editable:hover,
.ck-variable-hover {
    background-color: #ffe0b2;
    border-color: #f57c00;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(245, 124, 0, 0.2);
}

.ck-variable-editable::after {
    content: " ✏️";
    font-size: 0.8em;
    opacity: 0.7;
}

/* Modal Dialog */
.variable-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
}

.variable-editor-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.variable-editor-dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.variable-editor-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
}

.variable-editor-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.variable-editor-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.variable-editor-close:hover {
    background-color: #e9ecef;
    color: #495057;
}

.variable-editor-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.variable-editor-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    background-color: #f8f9fa;
}

/* Form Stilleri */
.variable-content {
    width: 100%;
    min-height: 120px;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: vertical;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.variable-content:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-check {
    margin-bottom: 0.5rem;
}

.form-check-input {
    margin-right: 0.5rem;
}

.form-check-label {
    color: #495057;
    cursor: pointer;
}

/* Buton Stilleri */
.btn {
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5c636a;
    border-color: #565e64;
}

/* Notification */
.variable-notification {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    animation: notificationSlideIn 0.3s ease-out;
}

@keyframes notificationSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.alert-success {
    background-color: #d1e7dd;
    border: 1px solid #badbcc;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c2c7;
    color: #842029;
}

/* Responsive */
@media (max-width: 768px) {
    .variable-editor-dialog {
        width: 95%;
        margin: 1rem;
    }
    
    .variable-editor-header,
    .variable-editor-body,
    .variable-editor-footer {
        padding: 1rem;
    }
    
    .variable-editor-footer {
        flex-direction: column;
    }
    
    .variable-editor-footer .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Tooltip */
.ck-variable-editable[title] {
    position: relative;
}

.ck-variable-editable[title]:hover::before {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.ck-variable-editable[title]:hover::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    z-index: 1000;
}
