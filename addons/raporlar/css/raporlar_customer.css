/**
 * Raporlar Yazdırma CSS
 * A4 standardında yazdırma için özel stiller
 */

/* Yazdırma için genel stiller */
@media print {
    /* <PERSON><PERSON> yapısı */
    @page {
        size: A4;
        margin: 1.5cm 2cm 2cm 2cm; /* Üst, sağ, alt, sol kenar boşlukları */
        padding-top: 20px;
        padding-bottom: 20px;
    }

    /* Yazdırma sırasında gizlenecek elementler */
    body {
        background: white !important;
        margin: 0;
        padding: 0px;
    }

    body * {
        visibility: hidden;
    }

    #printable-content,
    #printable-content * {
        visibility: visible;
    }

    #printable-content {
        position: relative;
        width: 100%;
        padding: 0;
        margin: 0;
        background: white;
        display: block;
        overflow: visible;
    }

    /* Yazdırma sırasında gizlenecek butonlar */
    #print-button,
    .card-toolbar,
    .header,
    .footer,
    .aside,
    .sidebar,
    .scrolltop,
    .toolbar,
    .drawer,
    .btn {
        display: none !important;
    }

    /* Sayfa sonu kontrolü */
    .category-item {
        page-break-after: auto;
        margin-bottom: 20px;
        padding-top: 10px; /* Kategori öğeleri arasında üst boşluk ekle */
    }

    /* Başlık ve içerik bir arada kalması için - ancak çok uzun içerikler için esnek davranır */
    .category-header-content {
        break-inside: auto; /* auto ile değiştirdik - gerektiğinde bölünebilir */
        page-break-inside: auto;
    }

    /* Sadece başlık ve ilk paragraf bir arada kalsın */
    .category-title,
    .first-paragraph {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    /* Başlık ve ilk paragraf birlikte kalmalı */
    .category-title + .first-paragraph {
        page-break-before: avoid;
    }

    /* İçerik sayfa sonu kontrolü */
    .category-content {
        overflow: visible;
    }

    /* Başlık stilleri */
    h1.category-title {
        font-size: 20pt;
        margin-bottom: 15pt;
    }

    h2.category-title {
        font-size: 18pt;
        margin-bottom: 12pt;
    }

    h3.category-title {
        font-size: 16pt;
        margin-bottom: 10pt;
    }

    h4.category-title,
    h5.category-title,
    h6.category-title {
        font-size: 14pt;
        margin-bottom: 8pt;
    }

    /* İçerik stilleri */
    .category-content {
        font-size: 12pt;
        line-height: 1.5;
        margin-top: 0.3cm; /* İçerik üst boşluğu */
        margin-bottom: 0.5cm; /* İçerik alt boşluğu */
    }

    /* Tablolar için stiller */
    table {
        width: 100%;
        border-collapse: collapse;
        page-break-inside: avoid;
        margin-bottom: 15pt;
        font-size: 12pt; /* Yazdırma için küçük yazı tipi */
    }

    table, th, td {
        border: 1px solid #ddd;
    }

    th, td {
        padding: 4px; /* Yazdırma için küçük padding */
        text-align: left;
    }

    /* Resimler için maksimum genişlik */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Sayfa sonları */
    .page-break {
        page-break-after: always;
    }

    /* CKEditor görsel hizalama yazdırma stilleri */
    .category-content .image-style-align-left,
    .category-content .image-style-align-right,
    .category-content .image-style-align-center,
    .category-content .image-style-side,
    .category-content figure.image.image-style-align-left,
    .category-content figure.image.image-style-align-right,
    .category-content figure.image.image-style-align-center,
    .category-content figure.image.image-style-side,
    .category-content figure.image-style-align-left,
    .category-content figure.image-style-align-right,
    .category-content figure.image-style-align-center,
    .category-content figure.image-style-side {
        display: block;
        margin: 10pt auto;
        float: none;
        max-width: 100%;
    }

    /* CKEditor tablo yazdırma stilleri */
    .category-content figure.table,
    .category-content .table,
    .category-content table {
        page-break-inside: avoid;
        width: 100% !important;
    }

    /* CKEditor liste yazdırma stilleri */
    .category-content ul,
    .category-content ol {
        padding-left: 20pt;
    }

    /* Temiz sayfa sonları */
    .category-content h1,
    .category-content h2,
    .category-content h3,
    .category-title {
        page-break-after: avoid;
        page-break-inside: avoid;
    }

    /* Önemli bölümlerin tek sayfada kalması */
    .category-item:has(h2:contains("SONUÇ")),
    .category-item:has(h2:contains("ÖNERİLER")),
    .category-item:has(h2:contains("SONUÇ VE ÖNERİLER")),
    .category-item:has(h2:contains("SONUC VE ONERILER")) {
        page-break-inside: avoid;
        page-break-before: always;
        margin-top: 1cm; /* Önemli bölümler için üst boşluk ekle */
    }

    /* Başlık ve içerik birlikte kalmalı */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }

    /* Başlık sonrası içerik ile birlikte kalmalı */
    h1 + *, h2 + *, h3 + *, h4 + *, h5 + *, h6 + * {
        page-break-before: avoid;
    }

    /* Uzun içerikler için sayfa sonu kontrolü */
    .category-content p {
        orphans: 4; /* Paragrafın en az 4 satırı bir sayfada kalır - arttırdık */
        widows: 4; /* Paragrafın en az 4 satırı yeni sayfaya geçer - arttırdık */
    }

    /* Sayfa sonunda çok az içerik kalmasını önle */
    .category-content {
        page-break-inside: auto; /* İçeriğin gerektiğinde bölünmesine izin ver */
    }

    /* Sayfa doluluk oranı kontrolü - bu kısım kaldırıldı çünkü üstte tanımlandı */

    /* Paragraflar arası sayfa sonu kontrolü */
    p {
        max-height: 75%; /* Bir paragraf sayfanın en fazla %75'ini kaplasın - daha fazla boşluk için azaltıldı */
        margin-bottom: 0.5cm; /* Paragraflar arası boşluk ekle */
    }

    /* Sayfa sonunda tek paragraf kalmasını önle */
    .category-content p:last-child {
        break-after: avoid;
        page-break-after: avoid;
    }

    /* Sayfa başında tek paragraf kalmasını önle */
    .category-content p:first-child {
        break-before: avoid;
        page-break-before: avoid;
    }

    /* Büyük içerikler için sayfa sonu kontrolü */
    .category-content img,
    .category-content table {
        max-height: 17cm; /* A4 sayfasına sığacak maksimum yükseklik - kenar boşlukları için azaltıldı */
    }

    /* Yazdırma sırasında float temizleme */
    .category-content:after {
        content: "";
        display: table;
        clear: both;
    }
}

/* .category-page{
    padding: 10px;
} */
/* Normal görünüm için stiller */
.category-title {
    color: #181C32;
    margin-bottom: 1rem;
}

.category-content {
    font-size: 1rem;
    line-height: 1.5;
}

/* Yazdırma butonu */
#print-button {
    cursor: pointer;
}

/* Rapor içeriği */
.report-content {
    margin-top: 20px;
}

/* Kategori öğeleri */
.category-item {
    margin-bottom: 30px;
    padding-top: 15px; /* Normal görünümde de üst boşluk ekle */
}

/* CKEditor içerik stilleri */

/* Tablolar için normal görünüm stilleri */
.category-content table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10px;
}

.category-content table,
.category-content th,
.category-content td {
    border: 1px solid #ddd;
}

.category-content th,
.category-content td {
    padding: 0.3rem; /* Metronic table-sm sınıfı için küçük padding */
    text-align: left;
}

/* CKEditor görsel hizalama stilleri */
.category-content img {
    max-width: 100%;
    height: auto;
    margin: 0 auto;
    display: block; /* Eklendi: Blok element olarak göster */
    text-align: center; /* Eklendi: İçeriği ortala */
}

/* CKEditor görsel stilleri */
.category-content figure.image,
.category-content .image {
    max-width: 100%;
    margin: 1em auto; /* Değişiklik: 0 yerine auto ile ortalama */
    text-align: center; /* Eklendi: İçeriği ortala */
    width: 100% !important;
}

/* CKEditor görsel hizalama stilleri */
.category-content .image-style-align-left,
.category-content figure.image.image-style-align-left,
.category-content figure.image-style-align-left {
    float: left;
    margin-right: 20px;
    margin-bottom: 10px;
}

.category-content .image-style-align-right,
.category-content figure.image.image-style-align-right,
.category-content figure.image-style-align-right {
    float: right;
    margin-left: 20px;
    margin-bottom: 10px;
}

.category-content .image-style-align-center,
.category-content figure.image.image-style-align-center,
.category-content figure.image-style-align-center {
    display: block;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

/* CKEditor yan görsel stili */
.category-content .image-style-side,
.category-content figure.image.image-style-side,
.category-content figure.image-style-side {
    float: right;
    margin-left: 20px;
    margin-bottom: 10px;
    max-width: 50%;
}

/* CKEditor liste stilleri */
.category-content ul,
.category-content ol {
    padding-left: 25px;
    margin-bottom: 15px;
}

.category-content ul li,
.category-content ol li {
    margin-bottom: 5px;
}

/* CKEditor blok alıntı stilleri */
.category-content blockquote {
    border-left: 5px solid #eee;
    padding: 10px 20px;
    margin: 0 0 20px;
    font-style: italic;
}

/* CKEditor tablo başlık stilleri */
.category-content table thead th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* CKEditor tablo hücre stilleri */
.category-content table[border="1"] {
    border-collapse: collapse;
}

.category-content table[border="1"] td,
.category-content table[border="1"] th {
    border: 1px solid #ddd;
}

/* CKEditor özel içerik stilleri */
.category-content figure.table,
.category-content .table {
    width: 100%;
    margin: 1em 0;
}

.category-content figure.image {
    display: inline-block;
    margin: 0;
    position: relative;
}

.category-content figure.image img {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    text-align: center;
}

.category-content figure figcaption,
.category-content .image figcaption {
    text-align: center;
    font-size: 0.8em;
    color: #666;
    margin-top: 5px;
}

/* CKEditor tablo stilleri */
.category-content table {
    border-collapse: collapse;
    width: 100%;
    font-size: 0.85rem; /* Metronic table-sm sınıfı için küçük yazı tipi */
}

.category-content table td,
.category-content table th {
    border: 1px solid #ddd !important;
    padding: 0.3rem !important; /* Metronic table-sm sınıfı için küçük padding */
}

/* CKEditor sayfa sonu */
.category-content .page-break {
    page-break-after: always;
    height: 0;
    display: block;
    border: 0;
}
