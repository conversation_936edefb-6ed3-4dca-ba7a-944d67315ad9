/**
 * <PERSON><PERSON><PERSON> Yazdırma CSS
 * A4 standardında yazdırma için özel stiller
 */

/* Yazd<PERSON>rma için genel stiller */
@media print {
    /* <PERSON><PERSON> yapı<PERSON>ı */
    @page {
        size: A4;
        margin: 1.5cm;
    }

    /* Yaz<PERSON><PERSON>rma sırasında gizlenecek elementler */
    body * {
        visibility: hidden;
    }

    #printable-content,
    #printable-content * {
        visibility: visible;
    }

    #printable-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        padding: 0;
        margin: 0;
        background: white;
    }

    /* Yazdırma sırasında gizlenecek butonlar */
    #print-button,
    .card-toolbar,
    .header,
    .footer,
    .aside,
    .sidebar,
    .scrolltop,
    .toolbar,
    .drawer,
    .btn {
        display: none !important;
    }

    /* <PERSON>fa sonu kontrolü */
    .category-item {
        page-break-inside: avoid;
        margin-bottom: 20px;
    }

    /* Başlık stilleri */
    h1.category-title {
        font-size: 20pt;
        margin-bottom: 15pt;
    }

    h2.category-title {
        font-size: 18pt;
        margin-bottom: 12pt;
    }

    h3.category-title {
        font-size: 16pt;
        margin-bottom: 10pt;
    }

    h4.category-title,
    h5.category-title,
    h6.category-title {
        font-size: 14pt;
        margin-bottom: 8pt;
    }

    /* İçerik stilleri */
    .category-content {
        font-size: 12pt;
        line-height: 1.5;
    }

    /* Tablolar için stiller */
    table {
        width: 100%;
        border-collapse: collapse;
        page-break-inside: avoid;
        margin-bottom: 15pt;
    }

    table, th, td {
        border: 1px solid #ddd;
    }

    th, td {
        padding: 8px;
        text-align: left;
    }

    /* Resimler için maksimum genişlik */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Sayfa sonları */
    .page-break {
        page-break-after: always;
    }

    /* CKEditor görsel hizalama yazdırma stilleri */
    .category-content .image-style-align-left,
    .category-content .image-style-align-right,
    .category-content .image-style-align-center {
        display: block;
        margin: 10pt auto;
        float: none;
    }

    /* CKEditor tablo yazdırma stilleri */
    .category-content figure.table {
        page-break-inside: avoid;
        width: 100% !important;
    }

    /* CKEditor liste yazdırma stilleri */
    .category-content ul,
    .category-content ol {
        padding-left: 20pt;
    }

    /* Temiz sayfa sonları */
    .category-content h1,
    .category-content h2,
    .category-content h3 {
        page-break-after: avoid;
    }

    .category-content p,
    .category-content li {
        page-break-inside: avoid;
    }

    /* Yazdırma sırasında float temizleme */
    .category-content:after {
        content: "";
        display: table;
        clear: both;
    }
}

/* Normal görünüm için stiller */
.category-title {
    color: #181C32;
    margin-bottom: 1rem;
}

.category-content {
    font-size: 1rem;
    line-height: 1.5;
}

/* Yazdırma butonu */
#print-button {
    cursor: pointer;
}

/* Rapor içeriği */
.report-content {
    margin-top: 20px;
}

/* Kategori öğeleri */
.category-item {
    margin-bottom: 30px;
}

/* CKEditor içerik stilleri */

/* Tablolar için normal görünüm stilleri */
.category-content table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.category-content table,
.category-content th,
.category-content td {
    border: 1px solid #ddd;
}

.category-content th,
.category-content td {
    padding: 8px;
    text-align: left;
}

/* CKEditor görsel hizalama stilleri */
.category-content img {
    max-width: 100%;
    height: auto;
}

.category-content .image-style-align-left {
    float: left;
    margin-right: 20px;
    margin-bottom: 10px;
}

.category-content .image-style-align-right {
    float: right;
    margin-left: 20px;
    margin-bottom: 10px;
}

.category-content .image-style-align-center {
    display: block;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

/* CKEditor liste stilleri */
.category-content ul,
.category-content ol {
    padding-left: 25px;
    margin-bottom: 15px;
}

.category-content ul li,
.category-content ol li {
    margin-bottom: 5px;
}

/* CKEditor blok alıntı stilleri */
.category-content blockquote {
    border-left: 5px solid #eee;
    padding: 10px 20px;
    margin: 0 0 20px;
    font-style: italic;
}

/* CKEditor tablo başlık stilleri */
.category-content table thead th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* CKEditor tablo hücre stilleri */
.category-content table[border="1"] {
    border-collapse: collapse;
}

.category-content table[border="1"] td,
.category-content table[border="1"] th {
    border: 1px solid #ddd;
}

/* CKEditor özel içerik stilleri */
.category-content figure.table {
    width: 100%;
    margin: 1em 0;
}

.category-content figure.image {
    display: inline-block;
    margin: 0;
}

.category-content figure.image img {
    display: block;
    margin: 0 auto;
}

.category-content figure figcaption {
    text-align: center;
    font-size: 0.8em;
    color: #666;
    margin-top: 5px;
}

/* CKEditor sayfa sonu */
.category-content .page-break {
    page-break-after: always;
    height: 0;
    display: block;
    border: 0;
}
