/**
 * <PERSON>or Değişken Sistemi CSS Dosyası
 * 
 * Bu dosya değişken sistemi için özel stilleri içerir.
 */

/* Değişken Paneli Stilleri */
.variables-list {
    max-height: 400px;
    overflow-y: auto;
}

.variable-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.variable-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.variable-item code {
    font-weight: bold;
    font-size: 0.9em;
    color: #198754;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
}

.variable-item:hover code {
    background-color: #e9ecef;
}

.variable-item small {
    display: block;
    margin-top: 0.25rem;
    color: #6c757d;
}

/* Değişken Kategorileri */
.variables-list h6.text-primary {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid #e4e6ef;
}

/* Önizleme Paneli */
#previewPanel .card-body {
    background-color: #f8f9fa;
}

#previewPanel .bg-white {
    background-color: white !important;
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem;
    padding: 1rem;
    min-height: 100px;
}

/* CKEditor İçindeki Değişken Vurgulama */
.ck-content .variable-placeholder {
    background-color: #e3f2fd;
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid #2196f3;
    color: #1976d2;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    font-weight: bold;
}

/* Değişken Butonları */
.btn-light-primary {
    background-color: rgba(54, 81, 207, 0.1);
    border-color: rgba(54, 81, 207, 0.2);
    color: #3651cf;
}

.btn-light-primary:hover {
    background-color: rgba(54, 81, 207, 0.2);
    border-color: rgba(54, 81, 207, 0.3);
    color: #3651cf;
}

.btn-light-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: rgba(23, 162, 184, 0.2);
    color: #17a2b8;
}

.btn-light-info:hover {
    background-color: rgba(23, 162, 184, 0.2);
    border-color: rgba(23, 162, 184, 0.3);
    color: #17a2b8;
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .variables-list .row .col-md-6 {
        margin-bottom: 0.5rem;
    }
    
    .variable-item {
        padding: 0.5rem;
    }
    
    .variable-item code {
        font-size: 0.8em;
    }
}

/* Animasyonlar */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.collapse.show {
    animation: fadeIn 0.3s ease-in-out;
}

/* Tooltip Stilleri */
.variable-item[data-bs-toggle="tooltip"] {
    position: relative;
}

/* Scrollbar Stilleri */
.variables-list::-webkit-scrollbar {
    width: 6px;
}

.variables-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.variables-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.variables-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Yazdırma Stilleri */
@media print {
    .variables-list,
    .btn-light-primary,
    .btn-light-info,
    #variablesPanel,
    #previewPanel {
        display: none !important;
    }
}
