<?php

/**
 * Değişken Sistemi Test Dosyası
 *
 * Bu dosya değişken sisteminin çalışıp çalışmadığını test etmek için kullanılır.
 * URL: /addons/raporlar/test_variables.php
 */

// Temel tanımlamalar
define('AREA', 'A');
define('USER_LOGIN', 1);
define('ISLETME_ID', 1);

// Ana dizini belirle
$root_dir = dirname(dirname(__DIR__));
require_once $root_dir . '/init.php';

// Test rapor ID'si
$test_rapor_id = 7; // Varsayılan test rapor ID'si

// Test içeriği
$test_content = '
<h2>Test Raporu</h2>
<p>{standart_giris_metni}</p>
<p>Bu rapor {firma_adi} tarafından hazırlanmıştır.</p>
<p>Rapor Tarihi: {rapor_tarihi}</p>
<p><PERSON>or Numarası: {rapor_no}</p>
<p>Müşteri: {musteri_adi}</p>
<p>Parsel Bilgisi: {parsel_bilgisi}</p>
<p>Proje Adı: {proje_adi}</p>
<p>Sondaj Sayısı: {sondaj_sayisi}</p>
<p>Bugünün Tarihi: {bugun_tarihi}</p>
<p>Firma Telefon: {firma_telefon}</p>
<p>Firma Adres: {firma_adres}</p>

<h3>Sonuç</h3>
<p>{standart_sonuc_metni}</p>

<h3>Yasal Uyarı</h3>
<p>{yasal_uyari}</p>

<h3>İmzalar</h3>
{imza_blogu}

<p>Tanımsız değişken testi: {tanimsiz_degisken}</p>
';

echo '<h1>Rapor Değişken Sistemi Test Sayfası</h1>';

echo '<h2>Orijinal İçerik:</h2>';
echo '<div style="background: #f5f5f5; padding: 10px; border: 1px solid #ddd; margin: 10px 0;">';
echo htmlspecialchars($test_content);
echo '</div>';

echo '<h2>İşlenmiş İçerik (Rapor ID: ' . $test_rapor_id . '):</h2>';
echo '<div style="background: #e8f5e8; padding: 10px; border: 1px solid #4CAF50; margin: 10px 0;">';

// Değişkenleri işle
$processed_content = process_rapor_variables($test_content, $test_rapor_id);
echo $processed_content;

echo '</div>';

echo '<h2>Kullanılabilir Değişkenler:</h2>';
echo '<div style="background: #fff3cd; padding: 10px; border: 1px solid #ffc107; margin: 10px 0;">';
echo render_variables_list();
echo '</div>';

echo '<h2>JavaScript Değişken Listesi:</h2>';
echo '<div style="background: #d1ecf1; padding: 10px; border: 1px solid #17a2b8; margin: 10px 0;">';
echo '<pre>' . htmlspecialchars(get_variables_for_javascript()) . '</pre>';
echo '</div>';

// Veritabanı test sorguları
echo '<h2>Veritabanı Test Sorguları:</h2>';
echo '<div style="background: #f8d7da; padding: 10px; border: 1px solid #dc3545; margin: 10px 0;">';

// Rapor bilgisi
$rapor = get_row('raporlar', "id=$test_rapor_id");
if ($rapor) {
    echo '<h3>Rapor Bilgisi:</h3>';
    echo '<pre>' . print_r($rapor, true) . '</pre>';
} else {
    echo '<p style="color: red;">Rapor ID ' . $test_rapor_id . ' bulunamadı!</p>';
}

// Kategori bilgisi
$categories = get_results('category', "object_type='rapor_doc' AND rapor_id=$test_rapor_id LIMIT 3");
if ($categories) {
    echo '<h3>İlk 3 Kategori:</h3>';
    echo '<pre>' . print_r($categories, true) . '</pre>';
} else {
    echo '<p style="color: red;">Bu rapor için kategori bulunamadı!</p>';
}

echo '</div>';

echo '<h2>Değişken Değerleri Test:</h2>';
echo '<div style="background: #e2e3e5; padding: 10px; border: 1px solid #6c757d; margin: 10px 0;">';

// Değişken tanımlamaları dosyasını dahil et
require_once ADDONS_DIR . '/raporlar/config/variables.php';

foreach ($rapor_variables as $name => $variable) {
    echo '<h4>{' . $name . '}</h4>';
    echo '<p><strong>Açıklama:</strong> ' . $variable['description'] . '</p>';
    echo '<p><strong>Tip:</strong> ' . $variable['type'] . '</p>';
    echo '<p><strong>Değer:</strong> ';

    $value = get_variable_value($name, $test_rapor_id);
    if ($value) {
        echo htmlspecialchars($value);
    } else {
        echo '<em style="color: #999;">Boş veya bulunamadı</em>';
    }
    echo '</p>';
    echo '<hr>';
}

echo '</div>';

echo '<h2>Test Linkleri:</h2>';
echo '<ul>';
echo '<li><a href="' . WEB_DIR . '/master/index.php?do=raporlar/variables_edit" target="_blank">Değişken Düzenleme Sayfası</a></li>';
echo '<li><a href="' . WEB_DIR . '/addons/raporlar/install_variables_table.php" target="_blank">Veritabanı Kurulum</a></li>';

if ($categories) {
    foreach (array_slice($categories, 0, 3) as $cat) {
        $link = WEB_DIR . '/master/index.php?do=raporlar/view&action=rapor&id=' . $cat->rapor_id . '&category_id=' . $cat->category_id;
        echo '<li><a href="' . $link . '" target="_blank">Kategori: ' . htmlspecialchars($cat->category) . ' (Rapor ID: ' . $cat->rapor_id . ')</a></li>';
    }
}
echo '</ul>';

echo '<h2>Düzenlenebilir Değişkenler Test:</h2>';
echo '<div style="background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 10px 0;">';
echo '<p><strong>CKEditor içinde test etmek için:</strong></p>';
echo '<ol>';
echo '<li>Yukarıdaki kategori linklerinden birine tıklayın</li>';
echo '<li>Düzenleme moduna geçin</li>';
echo '<li>Editöre şu değişkenleri yazın: <code>{standart_giris_metni}</code>, <code>{yasal_uyari}</code></li>';
echo '<li>Değişkenlere çift tıklayarak düzenleyin</li>';
echo '</ol>';
echo '</div>';

echo '<p><a href="javascript:history.back()">← Geri Dön</a></p>';
