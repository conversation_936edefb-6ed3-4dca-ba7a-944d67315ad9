
$(document).ready(function () {
    
      $( "div.draggable" ).draggable({
         axis: "y",
        start: function( event, ui ) { 
        // nesne sürüklenmeye başladığı anda yapılacak işlemler.
        //  console.log(event);
        //  console.log(ui);
        },  
        drag: function( event, ui ) {  
        //Sürüklenme anında yapılacak işlemler.
        //  console.log(event);
        //  console.log(ui);
        },
        stop: function( event, ui ) {
         //Sürükleme bittiği anda yapılacak işlemler.
        //  console.log(event);
        let data_id = $(ui.helper[0]).data('quick-id');
         console.log(ui.position.top);
         console.log(ui.position.left);
         $.ajax({
            type: 'POST',
            url: quick_update_dir,
            data: { actionmode: 'jeotek_log_position', data_id: data_id, top: ui.position.top, left: ui.position.left },
            success: function (r) {
              if (r.error) {
                swal.fire('Bi Hata Var', r.error, "error");
              }
            }
          });
        }
      });


    $('button.log_move_btn').click(function (e) {
        var input = $(this).parent().find('input.log_move').first();
        var val = parseFloat(input.val());
        console.info(val);
        if (val < 2) {
            log('Bu sayıdan daha küçük olamaz!', 'error');
        } else{
            input.val(val - 0.5 );
            input.trigger('change');
        }
        
    });
    $('button.log_move_down_btn').click(function (e) {
        var input = $(this).parent().find('input.log_move').first();
        var val = parseFloat(input.val());

  
            input.val(val + 0.5 );
            input.trigger('change');
      
        
    });
    $('input.log_move').change(delay(function (e) {
        var self = $(this);
        $.ajax({
            type: 'POST',
            url: quick_update_dir,
            data: { actionmode: 'jeotek_log', string: 'zemin_tanimi_for_log,baslangic', id: self.data('id'), reel_value: self.val() },
            success: function (r) {
              if (r.error) {
                swal.fire('Bi Hata Var', r.error, "error");
              }
            }
          });
    setTimeout(() => {
        location.reload();
    }, 600);
      }, 500));
});
