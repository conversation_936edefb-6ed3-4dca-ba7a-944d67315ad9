/**
 * CKEditor Değişken Düzenleme Plugin'i
 * 
 * Bu plugin, CKEditor içinde {degisken_adi} formatındaki değişkenleri
 * çift tıklayarak düzenlenebilir hale getirir.
 */

class VariableEditing extends Plugin {
    static get pluginName() {
        return 'VariableEditing';
    }

    init() {
        this._defineSchema();
        this._defineConverters();
        this._defineCommands();
        this._addEventListeners();
    }

    _defineSchema() {
        const schema = this.editor.model.schema;

        schema.register('variable', {
            allowWhere: '$text',
            isInline: true,
            isObject: true,
            allowAttributes: ['name', 'editable']
        });
    }

    _defineConverters() {
        const conversion = this.editor.conversion;

        // Model to view (editing)
        conversion.for('editingDowncast').elementToElement({
            model: 'variable',
            view: (modelElement, { writer: viewWriter }) => {
                const name = modelElement.getAttribute('name');
                const editable = modelElement.getAttribute('editable');
                
                const variableView = viewWriter.createContainerElement('span', {
                    class: 'ck-variable' + (editable ? ' ck-variable-editable' : ''),
                    'data-variable': name,
                    'data-editable': editable || 'false'
                });

                viewWriter.insert(viewWriter.createPositionAt(variableView, 0),
                    viewWriter.createText('{' + name + '}'));

                return variableView;
            }
        });

        // Model to view (data)
        conversion.for('dataDowncast').elementToElement({
            model: 'variable',
            view: (modelElement, { writer: viewWriter }) => {
                const name = modelElement.getAttribute('name');
                return viewWriter.createText('{' + name + '}');
            }
        });

        // View to model
        conversion.for('upcast').elementToElement({
            view: {
                name: 'span',
                classes: 'ck-variable'
            },
            model: (viewElement, { writer: modelWriter }) => {
                const name = viewElement.getAttribute('data-variable');
                const editable = viewElement.getAttribute('data-editable') === 'true';
                
                return modelWriter.createElement('variable', {
                    name: name,
                    editable: editable
                });
            }
        });

        // Text pattern için converter
        conversion.for('upcast').add(dispatcher => {
            dispatcher.on('text', (evt, data, conversionApi) => {
                const text = data.item.data;
                const variableRegex = /\{([a-zA-Z0-9_]+)\}/g;
                let match;
                const ranges = [];

                while ((match = variableRegex.exec(text)) !== null) {
                    ranges.push({
                        start: match.index,
                        end: match.index + match[0].length,
                        name: match[1]
                    });
                }

                if (ranges.length > 0) {
                    const { writer, consumable } = conversionApi;
                    
                    if (!consumable.consume(data.item, 'text')) {
                        return;
                    }

                    // Metni parçalara böl ve değişkenleri oluştur
                    let lastIndex = 0;
                    const elements = [];

                    for (const range of ranges) {
                        // Değişkenden önceki metin
                        if (range.start > lastIndex) {
                            elements.push(writer.createText(text.substring(lastIndex, range.start)));
                        }

                        // Değişken elementi
                        const isEditable = this._isVariableEditable(range.name);
                        elements.push(writer.createElement('variable', {
                            name: range.name,
                            editable: isEditable
                        }));

                        lastIndex = range.end;
                    }

                    // Son kısımdaki metin
                    if (lastIndex < text.length) {
                        elements.push(writer.createText(text.substring(lastIndex)));
                    }

                    // Orijinal text node'u değiştir
                    const parent = data.item.parent;
                    const index = parent.getChildIndex(data.item);
                    
                    writer.remove(data.item);
                    writer.insertChild(index, elements, parent);
                }
            });
        });
    }

    _defineCommands() {
        this.editor.commands.add('editVariable', new EditVariableCommand(this.editor));
    }

    _addEventListeners() {
        const view = this.editor.editing.view;
        const viewDocument = view.document;

        // Çift tıklama olayı
        this.listenTo(viewDocument, 'dblclick', (evt, data) => {
            const target = data.target;
            
            if (target.hasClass('ck-variable-editable')) {
                const variableName = target.getAttribute('data-variable');
                this._openVariableEditor(variableName, target);
                data.preventDefault();
                evt.stop();
            }
        });

        // Hover efekti
        this.listenTo(viewDocument, 'mouseover', (evt, data) => {
            const target = data.target;
            
            if (target.hasClass('ck-variable-editable')) {
                target.addClass('ck-variable-hover');
            }
        });

        this.listenTo(viewDocument, 'mouseout', (evt, data) => {
            const target = data.target;
            
            if (target.hasClass('ck-variable-editable')) {
                target.removeClass('ck-variable-hover');
            }
        });
    }

    _isVariableEditable(variableName) {
        // Düzenlenebilir değişkenleri kontrol et
        const editableVariables = [
            'standart_giris_metni',
            'standart_sonuc_metni',
            'yasal_uyari',
            'imza_blogu'
        ];
        
        return editableVariables.includes(variableName);
    }

    _openVariableEditor(variableName, targetElement) {
        // Modal dialog oluştur
        const modal = this._createVariableModal(variableName);
        document.body.appendChild(modal);
        
        // Modal'ı göster
        modal.style.display = 'block';
        
        // Mevcut değeri al ve textarea'ya yerleştir
        this._loadVariableValue(variableName, modal);
    }

    _createVariableModal(variableName) {
        const modal = document.createElement('div');
        modal.className = 'variable-editor-modal';
        modal.innerHTML = `
            <div class="variable-editor-backdrop"></div>
            <div class="variable-editor-dialog">
                <div class="variable-editor-header">
                    <h5>Değişken Düzenle: {${variableName}}</h5>
                    <button type="button" class="variable-editor-close">&times;</button>
                </div>
                <div class="variable-editor-body">
                    <div class="mb-3">
                        <label class="form-label">İçerik:</label>
                        <textarea class="form-control variable-content" rows="6" placeholder="Değişken içeriğini buraya yazın..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="scope" id="scope-global" value="global" checked>
                            <label class="form-check-label" for="scope-global">
                                Tüm raporlar için (Global)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="scope" id="scope-current" value="current">
                            <label class="form-check-label" for="scope-current">
                                Sadece bu rapor için
                            </label>
                        </div>
                    </div>
                </div>
                <div class="variable-editor-footer">
                    <button type="button" class="btn btn-secondary variable-editor-cancel">İptal</button>
                    <button type="button" class="btn btn-primary variable-editor-save">Kaydet</button>
                </div>
            </div>
        `;

        // Event listener'ları ekle
        this._addModalEventListeners(modal, variableName);

        return modal;
    }

    _addModalEventListeners(modal, variableName) {
        const closeBtn = modal.querySelector('.variable-editor-close');
        const cancelBtn = modal.querySelector('.variable-editor-cancel');
        const saveBtn = modal.querySelector('.variable-editor-save');
        const backdrop = modal.querySelector('.variable-editor-backdrop');

        // Kapatma olayları
        [closeBtn, cancelBtn, backdrop].forEach(element => {
            element.addEventListener('click', () => {
                this._closeModal(modal);
            });
        });

        // Kaydetme olayı
        saveBtn.addEventListener('click', () => {
            this._saveVariableValue(modal, variableName);
        });

        // ESC tuşu ile kapatma
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'block') {
                this._closeModal(modal);
            }
        });
    }

    _loadVariableValue(variableName, modal) {
        const textarea = modal.querySelector('.variable-content');
        
        // AJAX ile mevcut değeri al
        fetch('?do=raporlar/ajax_get_variable', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `variable_name=${encodeURIComponent(variableName)}&rapor_id=${this._getCurrentRaporId()}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                textarea.value = data.value || '';
            }
        })
        .catch(error => {
            console.error('Değişken değeri alınamadı:', error);
        });
    }

    _saveVariableValue(modal, variableName) {
        const textarea = modal.querySelector('.variable-content');
        const scope = modal.querySelector('input[name="scope"]:checked').value;
        const raporId = scope === 'current' ? this._getCurrentRaporId() : 0;
        
        // AJAX ile kaydet
        fetch('?do=raporlar/ajax_save_variable', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `variable_name=${encodeURIComponent(variableName)}&variable_value=${encodeURIComponent(textarea.value)}&rapor_id=${raporId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Başarı mesajı göster
                this._showNotification('Değişken başarıyla kaydedildi.', 'success');
                this._closeModal(modal);
                
                // Editördeki önizlemeyi güncelle
                this._updateVariablePreview(variableName, textarea.value);
            } else {
                this._showNotification('Değişken kaydedilemedi: ' + (data.error || 'Bilinmeyen hata'), 'error');
            }
        })
        .catch(error => {
            console.error('Değişken kaydedilemedi:', error);
            this._showNotification('Değişken kaydedilemedi.', 'error');
        });
    }

    _closeModal(modal) {
        modal.style.display = 'none';
        document.body.removeChild(modal);
    }

    _getCurrentRaporId() {
        // URL'den rapor ID'sini al
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id') || urlParams.get('category_id') || '0';
    }

    _updateVariablePreview(variableName, newValue) {
        // Editördeki değişken önizlemesini güncelle
        const previewPanel = document.getElementById('previewPanel');
        if (previewPanel && previewPanel.classList.contains('show')) {
            // Önizlemeyi yeniden yükle
            location.reload();
        }
    }

    _showNotification(message, type) {
        // Basit notification sistemi
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} variable-notification`;
        notification.innerHTML = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 300px;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}

// Command sınıfı
class EditVariableCommand extends Command {
    execute(variableName) {
        const plugin = this.editor.plugins.get('VariableEditing');
        plugin._openVariableEditor(variableName);
    }
}

// Plugin'i export et
window.VariableEditing = VariableEditing;
