// CKEditor Document entegrasyonu - DecoupledEditor versiyonu
$(document).ready(function () {
  // Global uploadEditorUrl değişkenini kontrol et
  if (typeof uploadEditorUrl === 'undefined') {
      // Eğer tanımlı değilse varsayılan bir değer ata
      window.uploadEditorUrl = '/index.php?do=upload/ckeditor';
  }

  // Sayfa yüklenirken önceki editörleri temizle
  $('.document-editor__toolbar').remove();
  $('.document-editor').removeClass('document-editor');

  // EVOCkeditor fonksiyonunu geçici olarak devre dışı bırak
  if (window.EVOCkeditor && window.EVOCkeditor.init) {
      var originalInit = window.EVOCkeditor.init;
      window.EVOCkeditor.init = function() {
          console.log('EVOCkeditor.init geçici olarak devre dışı bırakıldı');
      };

      // Sayfa kapanırken orijinal fonksiyonu geri yükle
      $(window).on('unload', function() {
          window.EVOCkeditor.init = originalInit;
      });
  }

  // Editör elementi kontrolü - Benzersiz sınıf ile
  var editorElement = document.querySelector('.rapor-doc-editor-unique');
  if (!editorElement) {
      console.error('Rapor Doc Editor elementi bulunamadı!');
      return;
  }

  // Editörün parent elementini al
  var editorContainer = editorElement.parentNode;
  if (!editorContainer) {
      console.error('Rapor Doc Editor parent elementi bulunamadı!');
      return;
  }

  // Araç çubuğu için container oluştur
  var toolbarContainer = document.createElement('div');
  toolbarContainer.id = 'toolbar-' + editorElement.id;
  toolbarContainer.classList.add('document-editor__toolbar');

  // Araç çubuğunu editörün üstüne yerleştir
  editorContainer.insertBefore(toolbarContainer, editorElement);

  // Editör container'a CSS sınıfı ekle
  editorContainer.classList.add('document-editor');

  // DecoupledEditor'u oluştur
  DecoupledEditor
      .create(editorElement, {
          // CKEditor Document konfigürasyonu
          language: 'tr',
          toolbar: {
              items: [
                  'heading',
                  '|',
                  'bold',
                  'italic',
                  'link',
                  'bulletedList',
                  'numberedList',
                  '|',
                  'outdent',
                  'indent',
                  '|',
                  'imageUpload',
                  'blockQuote',
                  'insertTable',
                  'undo',
                  'redo',
                  '|',
                  'alignment',
                  'fontColor',
                  'fontBackgroundColor',
                  'fontSize',
                  'fontFamily',
                  '|',
                  'horizontalLine',
                  'pageBreak',
                  'specialCharacters',
                  'removeFormat',
                  'sourceEditing'
              ]
          },
          ckfinder: {
              // Eğer uploadEditorUrl tanımlı değilse varsayılan bir URL kullan
              uploadUrl: WEB_DIR + '/index.php?do=rapor_doc/uploadimagefromeditor&ajax=1'
          },
          image: {
              toolbar: [
                  'imageTextAlternative',
                  'toggleImageCaption',
                  'imageStyle:inline',
                  'imageStyle:block',
                  'imageStyle:side'
              ]
          },
          table: {
              contentToolbar: [
                  'tableColumn',
                  'tableRow',
                  'mergeTableCells',
                  'tableCellProperties',
                  'tableProperties'
              ]
          },
          htmlSupport: {
              allow: [
                  {
                      name: /^.*$/,
                      styles: true,
                      attributes: true,
                      classes: true
                  }
              ]
          },
          // Tüm HTML içeriğine izin ver
          allowedContent: true,
          mediaEmbed: {
              previewsInData: true
          }
      })
      .then(function(editor) {
          // Araç çubuğunu oluşturulan div'e ekle
          toolbarContainer.appendChild(editor.ui.view.toolbar.element);

          // Form gönderilmeden önce içeriği gizli textarea'ya aktar
          var hiddenTextarea = document.getElementById('description_hidden');

          if (hiddenTextarea) {
              // Editör içeriği değiştiğinde gizli textarea'yı güncelle
              editor.model.document.on('change:data', function() {
                  // getData() doğrudan string döndürür, Promise döndürmez
                  var data = editor.getData();
                  hiddenTextarea.value = data;
              });

              // Form gönderilmeden önce içeriği güncelle
              var form = editorElement.closest('form');
              if (form) {
                  form.addEventListener('submit', function() {
                      // getData() doğrudan string döndürür, Promise döndürmez
                      var data = editor.getData();
                      hiddenTextarea.value = data;
                  });
              }
          }

          console.log('Rapor Doc Editor (DecoupledEditor) başlatıldı:', editor);

          // Değişken desteği ekle
          initVariableSupport(editor);

          // Değişken düzenleme desteği ekle
          initVariableEditing(editor);
      })
      .catch(function(error) {
          console.error('Rapor Doc Editor başlatılırken hata oluştu:', error);
      });
});

/**
 * Değişken desteği fonksiyonları
 */
function initVariableSupport(editor) {
    // Değişken listesi panelindeki öğelere tıklama olayı ekle
    $(document).on('click', '.variable-item', function(e) {
        // Eğer düzenleme ikonu tıklandıysa, düzenleme sayfasını aç
        if ($(e.target).hasClass('fa-edit')) {
            var variableName = $(this).data('variable');
            var currentUrl = new URL(window.location.href);
            var raporId = currentUrl.searchParams.get('id') || '0';
            var editUrl = '?do=raporlar/variables_edit&rapor_id=' + raporId + '#' + variableName;
            window.open(editUrl, '_blank');
            return;
        }

        var variableName = $(this).data('variable');
        var variableCode = '{' + variableName + '}';

        // Editöre değişkeni ekle
        editor.model.change(writer => {
            const insertPosition = editor.model.document.selection.getFirstPosition();
            writer.insertText(variableCode, insertPosition);
        });

        // Paneli kapat
        $('#variablesPanel').collapse('hide');

        // Editöre odaklan
        editor.editing.view.focus();
    });

    // Değişken vurgulama için CSS sınıfı ekle
    addVariableHighlighting();
}

/**
 * Değişken vurgulama CSS'i ekle
 */
function addVariableHighlighting() {
    if (!document.getElementById('variable-highlighting-css')) {
        var style = document.createElement('style');
        style.id = 'variable-highlighting-css';
        style.textContent = `
            .variable-item {
                cursor: pointer;
                transition: all 0.2s ease;
            }
            .variable-item:hover {
                background-color: #f8f9fa;
                border-color: #007bff !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .variable-item code {
                font-weight: bold;
                font-size: 0.9em;
            }
            .ck-content .variable-placeholder {
                background-color: #e3f2fd;
                padding: 2px 4px;
                border-radius: 3px;
                border: 1px solid #2196f3;
                color: #1976d2;
                font-family: monospace;
                font-size: 0.9em;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Değişken düzenleme desteği
 */
function initVariableEditing(editor) {
    // Editör içeriğini değişken vurgulama ile işle
    processVariablesInEditor(editor);

    // Editör içeriği değiştiğinde değişkenleri yeniden işle
    editor.model.document.on('change:data', () => {
        setTimeout(() => processVariablesInEditor(editor), 100);
    });
}

/**
 * Editördeki değişkenleri işle ve düzenlenebilir hale getir
 */
function processVariablesInEditor(editor) {
    const editingView = editor.editing.view;
    const viewDocument = editingView.document;

    // Editör içindeki tüm text node'ları bul
    editingView.change(writer => {
        const range = editingView.createRangeIn(editingView.document.getRoot());

        for (const item of range.getWalker()) {
            if (item.type === 'text') {
                const textNode = item.item;
                const text = textNode.data;

                // Değişken pattern'ini ara
                const variableRegex = /\{([a-zA-Z0-9_]+)\}/g;
                let match;
                const variables = [];

                while ((match = variableRegex.exec(text)) !== null) {
                    variables.push({
                        start: match.index,
                        end: match.index + match[0].length,
                        name: match[1],
                        full: match[0]
                    });
                }

                // Değişkenleri span'lara çevir
                if (variables.length > 0) {
                    wrapVariablesInSpans(writer, textNode, variables);
                }
            }
        }
    });

    // Çift tıklama event listener'ı ekle
    addVariableClickListeners(editor);
}

/**
 * Değişkenleri span elementleri ile sar
 */
function wrapVariablesInSpans(writer, textNode, variables) {
    const parent = textNode.parent;
    const text = textNode.data;
    let lastIndex = 0;
    const elements = [];

    for (const variable of variables) {
        // Değişkenden önceki metin
        if (variable.start > lastIndex) {
            const beforeText = text.substring(lastIndex, variable.start);
            if (beforeText) {
                elements.push(writer.createText(beforeText));
            }
        }

        // Değişken span'ı
        const isEditable = isVariableEditable(variable.name);
        const span = writer.createElement('span', {
            class: 'ck-variable' + (isEditable ? ' ck-variable-editable' : ''),
            'data-variable': variable.name,
            'data-editable': isEditable.toString(),
            title: isEditable ? 'Çift tıklayarak düzenle: ' + variable.name : variable.name
        });

        writer.insertChild(0, writer.createText(variable.full), span);
        elements.push(span);

        lastIndex = variable.end;
    }

    // Son kısımdaki metin
    if (lastIndex < text.length) {
        const afterText = text.substring(lastIndex);
        if (afterText) {
            elements.push(writer.createText(afterText));
        }
    }

    // Orijinal text node'u değiştir
    if (elements.length > 0) {
        const position = writer.createPositionBefore(textNode);
        writer.remove(textNode);
        writer.insert(position, elements);
    }
}

/**
 * Değişkenin düzenlenebilir olup olmadığını kontrol et
 */
function isVariableEditable(variableName) {
    const editableVariables = [
        'standart_giris_metni',
        'standart_sonuc_metni',
        'yasal_uyari',
        'imza_blogu'
    ];

    return editableVariables.includes(variableName);
}

/**
 * Değişken tıklama event listener'larını ekle
 */
function addVariableClickListeners(editor) {
    const editingView = editor.editing.view;
    const viewDocument = editingView.document;

    // Çift tıklama olayını dinle
    editor.listenTo(viewDocument, 'dblclick', (evt, data) => {
        const target = data.target;

        if (target.hasClass && target.hasClass('ck-variable-editable')) {
            const variableName = target.getAttribute('data-variable');
            if (variableName) {
                openVariableEditor(variableName);
                data.preventDefault();
                evt.stop();
            }
        }
    });
}

/**
 * Değişken düzenleme modal'ını aç
 */
function openVariableEditor(variableName) {
    // Modal HTML'ini oluştur
    const modalHtml = `
        <div class="variable-editor-modal" id="variableModal">
            <div class="variable-editor-backdrop"></div>
            <div class="variable-editor-dialog">
                <div class="variable-editor-header">
                    <h5>Değişken Düzenle: {${variableName}}</h5>
                    <button type="button" class="variable-editor-close">&times;</button>
                </div>
                <div class="variable-editor-body">
                    <div class="mb-3">
                        <label class="form-label">İçerik:</label>
                        <textarea class="form-control variable-content" rows="6" placeholder="Yükleniyor..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="scope" id="scope-global" value="global" checked>
                            <label class="form-check-label" for="scope-global">
                                Tüm raporlar için (Global)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="scope" id="scope-current" value="current">
                            <label class="form-check-label" for="scope-current">
                                Sadece bu rapor için
                            </label>
                        </div>
                    </div>
                </div>
                <div class="variable-editor-footer">
                    <button type="button" class="btn btn-secondary variable-editor-cancel">İptal</button>
                    <button type="button" class="btn btn-primary variable-editor-save">Kaydet</button>
                </div>
            </div>
        </div>
    `;

    // Modal'ı DOM'a ekle
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = document.getElementById('variableModal');

    // Modal'ı göster
    modal.style.display = 'block';

    // Event listener'ları ekle
    addModalEventListeners(modal, variableName);

    // Mevcut değeri yükle
    loadVariableValue(variableName, modal);
}

/**
 * Modal event listener'larını ekle
 */
function addModalEventListeners(modal, variableName) {
    const closeBtn = modal.querySelector('.variable-editor-close');
    const cancelBtn = modal.querySelector('.variable-editor-cancel');
    const saveBtn = modal.querySelector('.variable-editor-save');
    const backdrop = modal.querySelector('.variable-editor-backdrop');

    // Kapatma olayları
    [closeBtn, cancelBtn, backdrop].forEach(element => {
        element.addEventListener('click', () => {
            closeModal(modal);
        });
    });

    // Kaydetme olayı
    saveBtn.addEventListener('click', () => {
        saveVariableValue(modal, variableName);
    });

    // ESC tuşu ile kapatma
    const escHandler = (e) => {
        if (e.key === 'Escape') {
            closeModal(modal);
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
}

/**
 * Değişken değerini yükle
 */
function loadVariableValue(variableName, modal) {
    const textarea = modal.querySelector('.variable-content');
    const currentUrl = new URL(window.location.href);
    const raporId = currentUrl.searchParams.get('id') || currentUrl.searchParams.get('category_id') || '0';

    // AJAX ile mevcut değeri al
    fetch('?do=raporlar/ajax_get_variable', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `variable_name=${encodeURIComponent(variableName)}&rapor_id=${raporId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            textarea.value = data.value || '';
            textarea.placeholder = 'Varsayılan: ' + (data.default_value || '');
        } else {
            textarea.placeholder = 'Değer yüklenemedi: ' + (data.error || '');
        }
    })
    .catch(error => {
        console.error('Değişken değeri alınamadı:', error);
        textarea.placeholder = 'Değer yüklenemedi.';
    });
}

/**
 * Değişken değerini kaydet
 */
function saveVariableValue(modal, variableName) {
    const textarea = modal.querySelector('.variable-content');
    const scope = modal.querySelector('input[name="scope"]:checked').value;
    const currentUrl = new URL(window.location.href);
    const raporId = scope === 'current' ? (currentUrl.searchParams.get('id') || currentUrl.searchParams.get('category_id') || '0') : '0';

    // AJAX ile kaydet
    fetch('?do=raporlar/ajax_save_variable', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `variable_name=${encodeURIComponent(variableName)}&variable_value=${encodeURIComponent(textarea.value)}&rapor_id=${raporId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Değişken başarıyla kaydedildi.', 'success');
            closeModal(modal);

            // Sayfayı yenile (önizlemeyi güncellemek için)
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Değişken kaydedilemedi: ' + (data.error || 'Bilinmeyen hata'), 'error');
        }
    })
    .catch(error => {
        console.error('Değişken kaydedilemedi:', error);
        showNotification('Değişken kaydedilemedi.', 'error');
    });
}

/**
 * Modal'ı kapat
 */
function closeModal(modal) {
    modal.style.display = 'none';
    document.body.removeChild(modal);
}

/**
 * Bildirim göster
 */
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} variable-notification`;
    notification.innerHTML = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
