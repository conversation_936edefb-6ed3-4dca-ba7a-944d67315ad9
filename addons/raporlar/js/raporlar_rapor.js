// CKEditor Document entegrasyonu - DecoupledEditor versiyonu
$(document).ready(function () {
  // Global uploadEditorUrl değişkenini kontrol et
  if (typeof uploadEditorUrl === 'undefined') {
      // Eğer tanımlı değilse varsayılan bir değer ata
      window.uploadEditorUrl = '/index.php?do=upload/ckeditor';
  }

  // Sayfa yüklenirken önceki editörleri temizle
  $('.document-editor__toolbar').remove();
  $('.document-editor').removeClass('document-editor');

  // EVOCkeditor fonksiyonunu geçici olarak devre dışı bırak
  if (window.EVOCkeditor && window.EVOCkeditor.init) {
      var originalInit = window.EVOCkeditor.init;
      window.EVOCkeditor.init = function() {
          console.log('EVOCkeditor.init geçici olarak devre dışı bırakıldı');
      };

      // Sayfa kapanırken orijinal fonksiyonu geri yükle
      $(window).on('unload', function() {
          window.EVOCkeditor.init = originalInit;
      });
  }

  // Editör elementi kontrolü - Benzersiz sınıf ile
  var editorElement = document.querySelector('.rapor-doc-editor-unique');
  if (!editorElement) {
      console.error('Rapor Doc Editor elementi bulunamadı!');
      return;
  }

  // Editörün parent elementini al
  var editorContainer = editorElement.parentNode;
  if (!editorContainer) {
      console.error('Rapor Doc Editor parent elementi bulunamadı!');
      return;
  }

  // Araç çubuğu için container oluştur
  var toolbarContainer = document.createElement('div');
  toolbarContainer.id = 'toolbar-' + editorElement.id;
  toolbarContainer.classList.add('document-editor__toolbar');

  // Araç çubuğunu editörün üstüne yerleştir
  editorContainer.insertBefore(toolbarContainer, editorElement);

  // Editör container'a CSS sınıfı ekle
  editorContainer.classList.add('document-editor');

  // DecoupledEditor'u oluştur
  DecoupledEditor
      .create(editorElement, {
          // CKEditor Document konfigürasyonu
          language: 'tr',
          toolbar: {
              items: [
                  'heading',
                  '|',
                  'bold',
                  'italic',
                  'link',
                  'bulletedList',
                  'numberedList',
                  '|',
                  'outdent',
                  'indent',
                  '|',
                  'imageUpload',
                  'blockQuote',
                  'insertTable',
                  'undo',
                  'redo',
                  '|',
                  'alignment',
                  'fontColor',
                  'fontBackgroundColor',
                  'fontSize',
                  'fontFamily',
                  '|',
                  'horizontalLine',
                  'pageBreak',
                  'specialCharacters',
                  'removeFormat',
                  'sourceEditing'
              ]
          },
          ckfinder: {
              // Eğer uploadEditorUrl tanımlı değilse varsayılan bir URL kullan
              uploadUrl: WEB_DIR + '/index.php?do=rapor_doc/uploadimagefromeditor&ajax=1'
          },
          image: {
              toolbar: [
                  'imageTextAlternative',
                  'toggleImageCaption',
                  'imageStyle:inline',
                  'imageStyle:block',
                  'imageStyle:side'
              ]
          },
          table: {
              contentToolbar: [
                  'tableColumn',
                  'tableRow',
                  'mergeTableCells',
                  'tableCellProperties',
                  'tableProperties'
              ]
          },
          htmlSupport: {
              allow: [
                  {
                      name: /^.*$/,
                      styles: true,
                      attributes: true,
                      classes: true
                  }
              ]
          },
          // Tüm HTML içeriğine izin ver
          allowedContent: true,
          mediaEmbed: {
              previewsInData: true
          }
      })
      .then(function(editor) {
          // Araç çubuğunu oluşturulan div'e ekle
          toolbarContainer.appendChild(editor.ui.view.toolbar.element);

          // Form gönderilmeden önce içeriği gizli textarea'ya aktar
          var hiddenTextarea = document.getElementById('description_hidden');

          if (hiddenTextarea) {
              // Editör içeriği değiştiğinde gizli textarea'yı güncelle
              editor.model.document.on('change:data', function() {
                  // getData() doğrudan string döndürür, Promise döndürmez
                  var data = editor.getData();
                  hiddenTextarea.value = data;
              });

              // Form gönderilmeden önce içeriği güncelle
              var form = editorElement.closest('form');
              if (form) {
                  form.addEventListener('submit', function() {
                      // getData() doğrudan string döndürür, Promise döndürmez
                      var data = editor.getData();
                      hiddenTextarea.value = data;
                  });
              }
          }

          console.log('Rapor Doc Editor (DecoupledEditor) başlatıldı:', editor);

          // Değişken desteği ekle
          initVariableSupport(editor);
      })
      .catch(function(error) {
          console.error('Rapor Doc Editor başlatılırken hata oluştu:', error);
      });
});

/**
 * Değişken desteği fonksiyonları
 */
function initVariableSupport(editor) {
    // Değişken listesi panelindeki öğelere tıklama olayı ekle
    $(document).on('click', '.variable-item', function(e) {
        // Eğer düzenleme ikonu tıklandıysa, düzenleme sayfasını aç
        if ($(e.target).hasClass('fa-edit')) {
            var variableName = $(this).data('variable');
            var currentUrl = new URL(window.location.href);
            var raporId = currentUrl.searchParams.get('id') || '0';
            var editUrl = '?do=raporlar/variables_edit&rapor_id=' + raporId + '#' + variableName;
            window.open(editUrl, '_blank');
            return;
        }

        var variableName = $(this).data('variable');
        var variableCode = '{' + variableName + '}';

        // Editöre değişkeni ekle
        editor.model.change(writer => {
            const insertPosition = editor.model.document.selection.getFirstPosition();
            writer.insertText(variableCode, insertPosition);
        });

        // Paneli kapat
        $('#variablesPanel').collapse('hide');

        // Editöre odaklan
        editor.editing.view.focus();
    });

    // Değişken vurgulama için CSS sınıfı ekle
    addVariableHighlighting();
}

/**
 * Değişken vurgulama CSS'i ekle
 */
function addVariableHighlighting() {
    if (!document.getElementById('variable-highlighting-css')) {
        var style = document.createElement('style');
        style.id = 'variable-highlighting-css';
        style.textContent = `
            .variable-item {
                cursor: pointer;
                transition: all 0.2s ease;
            }
            .variable-item:hover {
                background-color: #f8f9fa;
                border-color: #007bff !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .variable-item code {
                font-weight: bold;
                font-size: 0.9em;
            }
            .ck-content .variable-placeholder {
                background-color: #e3f2fd;
                padding: 2px 4px;
                border-radius: 3px;
                border: 1px solid #2196f3;
                color: #1976d2;
                font-family: monospace;
                font-size: 0.9em;
            }
        `;
        document.head.appendChild(style);
    }
}
