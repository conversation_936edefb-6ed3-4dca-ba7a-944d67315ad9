$(document).ready(function () {
    $('a.force-popup').click(function (e) {
      e.preventDefault();
      e.stopPropagation();
      console.info($(this).attr('href'));

      const windowFeatures = "left=100,top=100,width=1600,height=auto,popup=yes";
      var features =' width=' + 1600 + ', height=' + window.outerHeight + ',location=0, resizable, scrollbars, toolbar=0, menubar=0';

      var myWindow =  window.open($(this).attr('href')+'&popup=1', "_blank", features);
 
    });

    $('#data_il_id').change(function(){
        let il_id = $(this).val();

        let il_adi = $(this).find(':selected').data('adi');
        let il_str = $('#data_il_adi');
        if (il_str.length > 0){
            il_str.remove();
            $('#data_il_id').append('<input type="hidden" id="data_il_adi" name="data[il]" value="'+il_adi+'"/>');
        }
        $.ajax({
            type: 'POST',
            url: WEB_DIR + '/index.php?do=options/get_ilce',
            data: { ajax: 1,il_id:il_id },
            success: function (r) {
              if (r.error) {
                swal.fire('Bi Hata Var', r.error, "error");
              }else{
                $('#data_ilce').html(r.options).select2("destroy").select2();

              }
            }
          });
    });

});
