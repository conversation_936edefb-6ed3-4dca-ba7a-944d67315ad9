$(document).ready(function () {
  $('#row_derinlik').repeater({
    initEmpty: false,

    defaultValues: {
        'text-input': 'foo'
    },

    show: function () {
        $(this).slideDown();
        $('input.data-mask').mask('00/00/0000', {
          placeholder: "dd/mm/yyyy",
          clearIfNotMatch: true
        });
    },

    hide: function (deleteElement) {
        $(this).slideUp(deleteElement);
    }
});

    $('button.btn_remove_log').click(function(){
        event.preventDefault();

        var url = $(this).attr('href');
        var dataId = $(this).data('id');
        swal.fire({
          title: lang.are_you_sure,
          text: '#' + dataId + lang.silmek_istiyorsun + lang.noback,
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: lang.yes_sure,
          cancelButtonText: lang.cancel
        }).then(function (result) {
          if (result.value) {
            $.ajax({
              type: 'POST',
              url: WEB_DIR + 'index.php?do=raporlar/view&action=sk&islem=delete_log',
              data: { dataId: dataId, ajax: 1 },
              success: function (r) {
                if (r.error) {
                  swal.fire(lang.bi_problem, r.error, "error");
                } else {
                  toastr.success(lang.silindi);
                  $('#row-' + dataId).remove();
                }
              }
            });
          }
        });
    });
    $('#ornek_turu').change(function(){
        let tur = $(this).val();
        
        if (tur == 'ud'){
            $('.spt_row, .karot_row, .presiyometre_row').removeClass('d-flex').addClass('d-none');
        }else{
            $('.presiyometre_row').removeClass('d-none').addClass('d-flex');
        }
        if (tur == 'spt'){
            $('.karot_row').removeClass('d-flex').addClass('d-none');
            $('.spt_row').removeClass('d-none').addClass('d-flex');
        }
        if (tur == 'karot'){
            $('.spt_row').removeClass('d-flex').addClass('d-none');
            $('.karot_row').removeClass('d-none').addClass('d-flex');
        }
    });
    $('#derinlik_baslangic').change(function(){
        let val = parseFloat($(this).val());
        val = val + 0.5;
        $('#derinlik_bitis').val(val);
    });
    $('input[name="30_45"], input[name="15_30"]').keyup(function(){
        stp_topla($(this).closest('div.d-flex').first());
    })
});

function stp_topla(conteiner){
    let val = parseFloat(conteiner.find('input[name="15_30"]').val());
    let diger = parseFloat(conteiner.find('input[name="30_45"]').val());
    if (val && diger)
        conteiner.find('input[name="n"]').val(val + diger).trigger('change');
}
