# Rapor Değişken Sistemi

Bu sistem, C<PERSON><PERSON>or iç<PERSON> `{degisken_adi}` formatında değişkenler kullanmanıza ve bunların dinamik olarak değiştirilmesine olanak sağlar.

## Özellikler

- ✅ CKEditor içinde değişken desteği
- ✅ Değişken listesi paneli
- ✅ Tıklayarak değişken ekleme
- ✅ Değişken önizleme sistemi
- ✅ Kategorize edilmiş değişkenler
- ✅ SQL, statik, ve fonksiyon tabanlı değişkenler
- ✅ Müşteri tarafında otomatik değişken işleme

## Kullanım

### 1. Editörde Değişken Kullanma

Rapor düzenleme sayfasında:
1. "Değişkenler" butonuna tıklayın
2. Kullanmak istediğiniz değişkene tıklayın
3. Değişken otomatik olarak editöre eklenecektir

### 2. <PERSON><PERSON> Yazma

Editörde doğ<PERSON> `{degisken_adi}` formatında yazabilirsiniz:
- `{parsel_bilgisi}` - Parsel bilgilerini gösterir
- `{firma_adi}` - Firma adını gösterir
- `{rapor_tarihi}` - Rapor tarihini gösterir

### 3. Önizleme

"Değişkenler İşlenmiş Önizleme" butonuna tıklayarak değişkenlerin nasıl görüneceğini görebilirsiniz.

## Mevcut Değişkenler

### Rapor Bilgileri
- `{rapor_tarihi}` - Raporun oluşturulma tarihi
- `{rapor_no}` - Rapor numarası
- `{bugun_tarihi}` - Güncel tarih

### Arazi Bilgileri
- `{parsel_bilgisi}` - Parsel bilgileri (İl/İlçe/Mahalle/Ada/Parsel)

### Firma Bilgileri
- `{firma_adi}` - JEOTEK Zemin Araştırmaları Ltd. Şti.
- `{firma_telefon}` - Firma telefon numarası
- `{firma_adres}` - Firma adresi

### Müşteri Bilgileri
- `{musteri_adi}` - Müşteri adı ve soyadı

### Proje Bilgileri
- `{proje_adi}` - Proje adı

### Sondaj Bilgileri
- `{sondaj_sayisi}` - Toplam sondaj sayısı

## Yeni Değişken Ekleme

Yeni değişken eklemek için `addons/raporlar/config/variables.php` dosyasını düzenleyin:

```php
'yeni_degisken' => [
    'label' => 'Yeni Değişken',
    'description' => 'Bu değişkenin açıklaması',
    'type' => 'sql', // sql, static, function
    'value' => 'SELECT ... FROM ... WHERE ...',
    'category' => 'Kategori Adı'
]
```

### Değişken Tipleri

1. **static**: Sabit değer
   ```php
   'type' => 'static',
   'value' => 'Sabit değer'
   ```

2. **sql**: Veritabanı sorgusu
   ```php
   'type' => 'sql',
   'value' => 'SELECT alan FROM tablo WHERE id = {rapor_id}'
   ```

3. **function**: PHP fonksiyonu
   ```php
   'type' => 'function',
   'value' => 'fonksiyon_adi'
   ```

## Dosya Yapısı

```
addons/raporlar/
├── config/
│   └── variables.php          # Değişken tanımlamaları
├── css/
│   └── raporlar_variables.css # Değişken sistemi CSS'i
├── js/
│   └── raporlar_rapor.js      # CKEditor entegrasyonu
├── admin/view/rapor/
│   └── rapor_form.php         # Düzenleme formu
├── customer/
│   └── view.php               # Müşteri görüntüleme
├── func.php                   # Değişken işleme fonksiyonları
└── test_variables.php         # Test sayfası
```

## Test Etme

Sistemi test etmek için:
1. `/addons/raporlar/test_variables.php` sayfasını ziyaret edin
2. Değişkenlerin doğru çalışıp çalışmadığını kontrol edin

## Sorun Giderme

### Değişkenler İşlenmiyor
- `process_rapor_variables()` fonksiyonunun çağrıldığından emin olun
- Rapor ID'sinin doğru geçildiğini kontrol edin

### Yeni Değişken Görünmüyor
- `variables.php` dosyasındaki syntax'ı kontrol edin
- Değişken adında özel karakter kullanmayın

### CSS Stilleri Yüklenmiyor
- `$page_css[]` array'ine CSS dosyasının eklendiğinden emin olun

## Güvenlik

- Tüm SQL sorguları `intval()` ile güvenli hale getirilir
- Değişken adları sadece alfanumerik karakterler içerebilir
- HTML çıktısı otomatik olarak escape edilir

## Performans

- Değişkenler sadece gerektiğinde işlenir
- SQL sorguları optimize edilmiştir
- CSS ve JS dosyaları minify edilebilir

## Gelecek Geliştirmeler

- [ ] Değişken önizleme editör içinde
- [ ] Koşullu değişkenler
- [ ] Değişken grupları
- [ ] İçe aktarma/dışa aktarma
- [ ] Değişken geçmişi
