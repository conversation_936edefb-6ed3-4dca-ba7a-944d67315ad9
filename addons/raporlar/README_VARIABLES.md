# Rapor Değişken Sistemi

Bu sistem, CKEditor içinde `{degisken_adi}` formatında değişkenler kullanmanıza ve bunların dinamik olarak değiştirilmesine olanak sağlar.

## Özellikler

- ✅ CKEditor içinde değişken desteği
- ✅ Değişken listesi paneli
- ✅ Tıklayarak değişken ekleme
- ✅ Değişken önizleme sistemi
- ✅ Kategorize edilmiş <PERSON>ğ<PERSON>şkenler
- ✅ SQL, statik, ve fonksiyon tabanlı değişkenler
- ✅ **Düzenlenebilir değişkenler** (Yeni!)
- ✅ Rapor özel ve global değişken yönetimi
- ✅ Değişken düzenleme arayüzü
- ✅ Müşteri tarafında otomatik değişken işleme

## Kullanım

### 1. Editörde Değişken Kullanma

Rapor düzenleme sayfasında:

1. "Değ<PERSON>şkenler" butonuna tıklayın
2. Kullanmak istediğiniz değişkene tıklayın
3. Değişken otomatik olarak editöre eklenecektir

### 2. Manuel Değişken Yazma

Editörde doğrudan `{degisken_adi}` formatında yazabilirsiniz:

- `{parsel_bilgisi}` - Parsel bilgilerini gösterir
- `{firma_adi}` - Firma adını gösterir
- `{rapor_tarihi}` - Rapor tarihini gösterir

### 3. Önizleme

"Değişkenler İşlenmiş Önizleme" butonuna tıklayarak değişkenlerin nasıl görüneceğini görebilirsiniz.

### 4. Düzenlenebilir Değişkenler (Yeni!)

Bazı değişkenlerin içeriğini editör içinde düzenleyebilirsiniz:

1. **Global Düzenleme**: Tüm raporlar için geçerli olan değişkenleri düzenleyin

   - "Global Değişkenler" butonuna tıklayın
   - Değişkenleri düzenleyin ve kaydedin

2. **Rapor Özel Düzenleme**: Sadece belirli bir rapor için özel değerler belirleyin

   - "Değişkenleri Düzenle" butonuna tıklayın
   - O rapor için özel değerler girin

3. **Düzenlenebilir Değişken Türleri**:
   - `{standart_giris_metni}` - Standart giriş metni
   - `{standart_sonuc_metni}` - Standart sonuç metni
   - `{yasal_uyari}` - Yasal uyarı metni
   - `{imza_blogu}` - İmza bloğu (HTML destekli)

## Mevcut Değişkenler

### Rapor Bilgileri

- `{rapor_tarihi}` - Raporun oluşturulma tarihi
- `{rapor_no}` - Rapor numarası
- `{bugun_tarihi}` - Güncel tarih

### Arazi Bilgileri

- `{parsel_bilgisi}` - Parsel bilgileri (İl/İlçe/Mahalle/Ada/Parsel)

### Firma Bilgileri

- `{firma_adi}` - JEOTEK Zemin Araştırmaları Ltd. Şti.
- `{firma_telefon}` - Firma telefon numarası
- `{firma_adres}` - Firma adresi

### Müşteri Bilgileri

- `{musteri_adi}` - Müşteri adı ve soyadı

### Proje Bilgileri

- `{proje_adi}` - Proje adı

### Sondaj Bilgileri

- `{sondaj_sayisi}` - Toplam sondaj sayısı

### Düzenlenebilir Metinler

- `{standart_giris_metni}` - Standart giriş metni (düzenlenebilir)
- `{standart_sonuc_metni}` - Standart sonuç metni (düzenlenebilir)
- `{yasal_uyari}` - Yasal uyarı metni (düzenlenebilir)
- `{imza_blogu}` - İmza bloğu (düzenlenebilir, HTML destekli)

## Yeni Değişken Ekleme

Yeni değişken eklemek için `addons/raporlar/config/variables.php` dosyasını düzenleyin:

```php
'yeni_degisken' => [
    'label' => 'Yeni Değişken',
    'description' => 'Bu değişkenin açıklaması',
    'type' => 'sql', // sql, static, function
    'value' => 'SELECT ... FROM ... WHERE ...',
    'category' => 'Kategori Adı'
]
```

### Değişken Tipleri

1. **static**: Sabit değer

   ```php
   'type' => 'static',
   'value' => 'Sabit değer'
   ```

2. **sql**: Veritabanı sorgusu

   ```php
   'type' => 'sql',
   'value' => 'SELECT alan FROM tablo WHERE id = {rapor_id}'
   ```

3. **function**: PHP fonksiyonu
   ```php
   'type' => 'function',
   'value' => 'fonksiyon_adi'
   ```

## Dosya Yapısı

```
addons/raporlar/
├── config/
│   └── variables.php          # Değişken tanımlamaları
├── css/
│   └── raporlar_variables.css # Değişken sistemi CSS'i
├── js/
│   └── raporlar_rapor.js      # CKEditor entegrasyonu
├── admin/view/rapor/
│   └── rapor_form.php         # Düzenleme formu
├── customer/
│   └── view.php               # Müşteri görüntüleme
├── func.php                   # Değişken işleme fonksiyonları
└── test_variables.php         # Test sayfası
```

## Test Etme

Sistemi test etmek için:

1. `/addons/raporlar/test_variables.php` sayfasını ziyaret edin
2. Değişkenlerin doğru çalışıp çalışmadığını kontrol edin

## Sorun Giderme

### Değişkenler İşlenmiyor

- `process_rapor_variables()` fonksiyonunun çağrıldığından emin olun
- Rapor ID'sinin doğru geçildiğini kontrol edin

### Yeni Değişken Görünmüyor

- `variables.php` dosyasındaki syntax'ı kontrol edin
- Değişken adında özel karakter kullanmayın

### CSS Stilleri Yüklenmiyor

- `$page_css[]` array'ine CSS dosyasının eklendiğinden emin olun

## Güvenlik

- Tüm SQL sorguları `intval()` ile güvenli hale getirilir
- Değişken adları sadece alfanumerik karakterler içerebilir
- HTML çıktısı otomatik olarak escape edilir

## Performans

- Değişkenler sadece gerektiğinde işlenir
- SQL sorguları optimize edilmiştir
- CSS ve JS dosyaları minify edilebilir

## Gelecek Geliştirmeler

- [ ] Değişken önizleme editör içinde
- [ ] Koşullu değişkenler
- [ ] Değişken grupları
- [ ] İçe aktarma/dışa aktarma
- [ ] Değişken geçmişi
