<?php
function zemin_rengi_belirle($zemin_sinifi)
{
    global $zemin_renkleri;

    foreach ($zemin_renkleri as $k => $v) {
        if (in_array(strtoupper($zemin_sinifi), $v[1])) {
            return $k;
        }
    }
    return '';
}
function render_sondaj_kabul_nilufer($rapor_id, $part)
{
    $rapor = get_row('raporlar', "id=" . $rapor_id);

    $part = $part * 6;
    $part_end = $part + 6;

    $sklar = get_results('sklar', "rapor_id=$rapor->id LIMIT $part,$part_end");

    $main_sondaj_bilgisi = $sklar[0];

    $sorumlu_muh = get_row('users', "user_id=$main_sondaj_bilgisi->sorumlu_muh");
    $header = '<table class="table table-sm align-center fw-senmibol table-bordered m-0 align-center" width="100%">
                    <tbody>
                        <tr>
                            <td colspan="9" align="center">
                                <strong>SONDAJ KUYUSU KABUL TUTANAĞI</strong>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4" width="297" class="bg-secondary">
                                İşin Adı
                            </td>
                            <td colspan="5" width="408">' . $rapor->proje_adi . '</td>
                        </tr>
                        <tr>
                            <td colspan="4" width="297" class="bg-secondary">
                                İl / İl&ccedil;e / Mahalle
                            </td>
                            <td colspan="5" width="408">
                                <span class="text-uppercase">' . $rapor->il . ' / ' . $rapor->ilce . ' / ' . $rapor->mahalle . '</span>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4" width="297" class="bg-secondary">
                                Pafta / Ada / Parsel
                            </td>
                            <td colspan="5" width="408">
                                <span class=" text-uppercase">' . $rapor->pafta . ' / ' . $rapor->ada . ' / ' . $rapor->parsel . '</span>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4" width="297" class="bg-secondary">
                                Sondajın T&uuml;r&uuml; / Uygulama Şekli
                            </td>
                            <td colspan="5" width="408">
                                ' . $main_sondaj_bilgisi->makine_tipi . '
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4" width="297" class="bg-secondary">
                                Sondaj Makinesinin T&uuml;r&uuml; / Tipi / Şahmerdan
                            </td>
                            <td colspan="5" width="408">
                                ' . $main_sondaj_bilgisi->sahmerdan_tipi . '
                            </td>
                        </tr>
                    </tbody>
                </table>';

    $content = '<table class="table table-sm align-center fw-senmibol table-bordered align-center m-0" width="100%"> <tbody>';

    $content .= '<tr>
            <td width="96" class="bg-secondary">SK No</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">' . $v->adi . '</td>';
    }
    $content .= '</tr>';

    $content .= '<tr>
            <td width="96" class="bg-secondary">Kot</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">' . $v->sondaj_kotu . '</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Enlem</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">' . $v->latitude . '</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Boylam</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">' . $v->longitude . '</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Başlama</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">' . unix_to_date($v->baslama_tarihi, 1) . '</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Bitiş</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">' . unix_to_date($v->bitis_tarihi, 1) . '</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Kontrol</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">-</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Ölçüm</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">-</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Derinlik</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">' . $v->sondaj_derinligi . '</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Muhafaza</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">-</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">SPT</td>';
    foreach ($sklar as $v) {
        $spt_sayisi = say(get_results('log', "sk_id=$v->id AND ornek_turu<>'ud'", 'id'));
        $content .= '<td  align="center">' . $spt_sayisi . ' adet</td>';
    }
    $content .= '</tr>';

    $content .= '<tr>
            <td width="96" class="bg-secondary">UD</td>';
    foreach ($sklar as $v) {
        $spt_sayisi = say(get_results('log', "sk_id=$v->id AND ornek_turu='ud'", 'id'));
        $content .= '<td  align="center">' . $spt_sayisi . ' adet</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Presiyometre</td>';
    foreach ($sklar as $v) {
        $spt_sayisi = say(get_results('log', "sk_id=$v->id AND elastisite!=''", 'id'));
        $content .= '<td  align="center">' . $spt_sayisi . ' adet</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">BST</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">-</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Veyn</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">-</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Yass</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">8,00</td>';
    }
    $content .= '</tr>';
    $content .= '<tr>
            <td width="96" class="bg-secondary">Formasyon</td>';
    foreach ($sklar as $v) {
        $content .= '<td  align="center">Tmm</td>';
    }
    $content .= '</tr>';



    $content .= ' <tr>
        <td colspan="7">
            <p class="text-center px-5  mb-0">Yukarıda bilgileri belirtilen sondaj kuyuları JEOTEK SONDAJ MÜH. tarafından açılarak gerekli tespit ve deneyler yapılmış olup, iş bu tutanak tanzim ve imza edilmiştir.</p>
        </td>
    </tr>';
    $content .= ' <tr>
        <td colspan="7" class="bg-secondary" align="center">Sondaj Lokasyon Krokisi</td>
    </tr>';

    // Sondaj resmi
    $images_dir = images_url('raporlar', 'url') .  'kroki' . DS;
    $kroki = $images_dir . $rapor->sondaj_kroki;
    $content .= ' <tr>
        <td colspan="7" align="center"><img src="' . $kroki . '" class="mh-400px" alt=""></td>
    </tr>';

    $content .= ' </tbody> </table>';
    $footer = '<table class="table table-sm align-center fw-senmibol table-bordered align-center m-0" width="100%">
<tbody>
    <tr>
        <td align="center" class="bg-secondary">Taşeron Firma</td>
        <td align="center" class="bg-secondary">Y&uuml;klenici Firma</td>
        <td align="center" class="bg-secondary">İlgili İdare</td>
    </tr>
    <tr>
        <td align="center">JEOTEK SONDAJ M&Uuml;H.</td>
        <td align="center">JEOTEK SONDAJ M&Uuml;H.</td>
        <td align="center">NİL&Uuml;FER BELEDİYESİ</td>
    </tr>
    <tr>
        <td align="center" class="bg-secondary">Arazi M&uuml;hendisi</td>
        <td align="center" class="bg-secondary">Sorumlu M&uuml;hendis</td>
        <td align="center" class="bg-secondary">Kontrol M&uuml;hendisi</td>
    </tr>
    <tr>
        <td align="center">
            <p>' . $sorumlu_muh->name . ' ' . $sorumlu_muh->lastname . '</p>
            <p>Jeoloji M&uuml;h.</p>
            <p>Oda Sicil: 9677</p>
        </td>
        <td align="center">
            <p>&Ouml;ZG&Uuml;R YILMAZ</p>
            <p>Jeoloji M&uuml;h.</p>
            <p>Oda Sicil: 9677</p>
        </td>
        <td align="center"></td>
    </tr>
</tbody>
</table>';
    return $header . $content . $footer;
}
function render_sondaj_kabul_osmangazi($rapor_id, $part)
{
    $rapor = get_row('raporlar', "id=" . $rapor_id);
    $sk = get_row('sklar', "rapor_id=$rapor->id  AND id=$part");
    $tumsklar = get_results('sklar', "rapor_id=$rapor->id", 'id');
    $sorumlu_muh = get_row('users', "user_id=$sk->sorumlu_muh");
    $header  = '<table class="table table-sm align-center fw-senmibol table-bordered align-center m-0" width="100%"><tbody>';
    $header .= '<tr><td align="center" colspan="2"><strong>SONDAJ KUYUSU KABUL TUTANAĞI</strong></td></tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="133">İşin Adı</td>
        <td>' . $rapor->proje_adi . '</td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Sondaj Numarası</td>
        <td>' . $sk->adi . '</td>
        </tr>';

    $header .= '<tr>
        <td class="bg-secondary" width="400">Arsa Plankotesine Göre Sondaj Üst Kotu (m)</td>
        <td>~88,04</td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Sondajın Türü (Zemin / Kaya)</td>
        <td>ZEMİN</td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Sondajın Uygulama Şekli (Burgulu–burgusuz /sulu-susuz) </td>
        <td>ZEMİN</td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Sondaj Makinesinin Türü (Marka/Model)</td>
        <td><span class="text-uppercase">' . $sk->makine_tipi . '</span></td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Başlama Tarihi</td>
        <td><span class="text-uppercase">' . unix_to_date($sk->baslama_tarihi, 1) . '</span></td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Bitiş Tarihi</td>
        <td><span class="text-uppercase">' . unix_to_date($sk->bitis_tarihi, 1) . '</span></td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Sondaj Derinliği (m)</td>
        <td><span class="text-uppercase">' . $sk->sondaj_derinligi . 'm</span></td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Sondaj Noktalarının Koordinatları (WGS-84)</td>
        <td><span class="text-uppercase"> E: ' . $sk->latitude . '</span> <span class="text-uppercase"> B: ' . $sk->longitude . '</span></td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Kuyu Çapı/Karot Çapı</td>
        <td><span class="text-uppercase">' . $sk->delgi_capi . 'mm</span> </td>
        </tr>';

    $spt_sayisi = say(get_results('log', "sk_id=$part AND ornek_turu<>'ud'", 'id'));
    $header .= '<tr>
        <td class="bg-secondary" width="400">Alınan Örselenmiş Numune Sayısı (adet)</td>
        <td><span class="text-uppercase">' . $spt_sayisi . ' adet</span> </td>
        </tr>';
    $spt_sayisi = say(get_results('log', "sk_id=$part AND ornek_turu='ud'", 'id'));
    $header .= '<tr>
        <td class="bg-secondary" width="400">Alınan Örselenmemiş Numune Sayısı (adet)</td>
        <td><span class="text-uppercase">' . $spt_sayisi . ' adet</span> </td>
        </tr>';
    $spt_sayisi = say(get_results('log', "sk_id=$part ", 'id'));
    $header .= '<tr>
        <td class="bg-secondary" width="400">SPT Adedi</td>
        <td><span class="text-uppercase">' . $spt_sayisi . ' adet</span> </td>
        </tr>';
    $spt_sayisi = say(get_results('log', "sk_id=$part AND elastisite!=''", 'id'));
    $header .= '<tr>
        <td class="bg-secondary" width="400">Presiyometre Adedi</td>
        <td><span class="text-uppercase">' . $spt_sayisi . ' adet</span> </td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">BST / Permeabilite Adedi</td>
        <td><span class="text-uppercase">Yok</span> </td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Kuyu İçi Veyn Adedi</td>
        <td><span class="text-uppercase">Yok</span> </td>
        </tr>';
    $header .= '<tr>
        <td class="bg-secondary" width="400">Muhafaza Borusu Boyu</td>
        <td><span class="text-uppercase">Yok</span> </td>
        </tr>';

    $derinlik = get_row('yeralti_sulari', "sk_id=$part order by id desc");
    $header .= '<tr>
        <td class="bg-secondary" width="400">Yeraltı Suyu Seviyesi Derinliği (m)</td>
        <td>' . $derinlik->derinlik . '</td>
        </tr>';

    $header .= '<tr><td align="center" colspan="2">Yukarıda belirtilen sondaj kuyusu JEOTEK SONDAJ MÜHENDİSLİK tarafından ' . date('d.m.Y') . ' tarihinde gerekli tespit ve deneyler yapılmış olup, iş bu tutanak ' . say($tumsklar) . ' nüsha olarak tanzim ve imza edilmiştir.         </td></tr>';


    // Sondaj resmi
    $images_dir = images_url('raporlar', 'url') .  'kroki' . DS;
    $kroki = $images_dir . $rapor->sondaj_kroki;
    $header .= ' <tr>
            <td colspan="2" align="center"><img src="' . $kroki . '" class="mh-300px" alt=""><p class="text-muted text-xs">Sondaj Lokasyon Krokisi</p></td>
        </tr>';

    $header .= '</tbody></table>';
    $footer = '<table class="table table-sm align-center fw-senmibol table-bordered align-center m-0" width="100%">
    <tbody>
        <tr>
            <td align="center" class="bg-secondary">Yüklenici Firma</td>
            <td align="center" class="bg-secondary">Kontrol Mühendisi</td>
        </tr>
        <tr>
            <td align="center">JEOTEK SONDAJ M&Uuml;H.</td>
            <td align="center">JEOTEK SONDAJ M&Uuml;H.</td>
        </tr>
        <tr>
            <td align="center" class="bg-secondary">Arazi M&uuml;hendisi</td>
            <td align="center" class="bg-secondary">Sorumlu M&uuml;hendis</td>
        </tr>
        <tr>
            <td align="center">
                <p>' . $sorumlu_muh->name . ' ' . $sorumlu_muh->lastname . '</p>
                <p>Jeoloji M&uuml;h.</p>
                <p>Oda Sicil: 9677</p>
            </td>
            <td align="center">
                <p>&Ouml;ZG&Uuml;R YILMAZ</p>
                <p>Jeoloji M&uuml;h.</p>
                <p>Oda Sicil: 9677</p>
            </td>
        </tr>
    </tbody>
    </table>';
    return $header . $footer;
}

function get_zemin_tanimi_to_log($rapor_id, $secili_sk)
{
    $sonuc = get_results('zemin_tanimi_for_log', "rapor_id=$rapor_id AND sk='$secili_sk'", "", ARRAY_A);
    $result = array();
    foreach ($sonuc as $item) {
        $result[$item['render_col']] = $item;
    }
    return $result;
}
function render_zemin_tanimi_to_log($rapor_id, $secili_sk, $last_log_row)
{

    // Kayitli bir sonuc varsa, onu dondur yoksa, render et
    // 10 Subat 2025 13:22
    // Annem halsiz icin anadolu hastane 16.00 ameliyat apandis.
    $varmi = get_row('zemin_tanimi_for_log', "rapor_id=$rapor_id AND sk='$secili_sk'", 'id');
    if ($varmi) {

        return get_zemin_tanimi_to_log($rapor_id, $secili_sk);
    }
    $zemin_tanimlari = get_results('zemin_tanimi', "rapor_id=$rapor_id AND sk='$secili_sk' order by position");

    $grouped_zemin_tanimlari = array();
    $previous_zemin_sinifi = null;
    $current_group = null;

    foreach ($zemin_tanimlari as $z) {
        if ($z->zemin_sinifi !== $previous_zemin_sinifi) {
            if ($current_group) {
                $grouped_zemin_tanimlari[] = $current_group;
            }
            $current_group = array(
                'zemin_sinifi' => $z->zemin_sinifi,
                'baslangic' => $z->baslangic,
                'bitis' => $z->bitis,
                'zemin_rengi' => $z->zemin_rengi,
                'tanim' => $z->tanim,

            );
        } else {
            $current_group['bitis'] = $z->bitis;
            $current_group['rowspan'] = true;
        }
        $previous_zemin_sinifi = $z->zemin_sinifi;
    }

    if ($current_group) {
        $grouped_zemin_tanimlari[] = $current_group;
    }


    $render = array();
    $onceki_bitis = 0;

    foreach ($grouped_zemin_tanimlari as $k => $v) {

        // Bitis degerini baslangic degerine yuvarla
        // $v['baslangic'] = round_zemin($v['bitis']);
        if ($k == count($grouped_zemin_tanimlari) - 1) {
            $v['bitis'] = $last_log_row->derinlik_bitis;
        }


        if ($k != 0)
            $v['baslangic_round'] = round_zemin($onceki_bitis);
        else
            $v['baslangic_round'] = round_zemin($v['baslangic']);

        if ($v['rowspan'] or (($v['baslangic']) < $v['bitis'])) {
            $v['round_bitis'] = round_zemin($v['bitis']);
            $v['rowspan'] = true;
            $rowspan = ($v['round_bitis'] - $v['baslangic_round']) * 2;
            $v['rowspan_row'] = $rowspan;
        } else {
            $v['round_bitis'] = round_zemin($v['bitis']);
        }

        if (strpos($v['baslangic_round'], '.') !== false) {
            $s_key = rtrim(rtrim($v['baslangic_round'], '0'), '.');
        } else {
            $s_key = $v['baslangic_round'];
        }


        $v['derinlik_aciklama'] = $v['round_bitis'] . ' m';

        $render['row_' . str_replace('.', '_', $s_key)] = $v;

        $onceki_bitis = $v['bitis'];
    }

    // Bu verileri sisteme kaydedelim
    foreach ($render as $k => $v) {
        $_data = array();
        $_data['rapor_id'] = $rapor_id;
        $_data['sk'] = $secili_sk;

        $_data['render_col'] = $k;
        $_data['zemin_sinifi'] = $v['zemin_sinifi'];
        $_data['baslangic'] = $v['baslangic'];
        $_data['bitis'] = $v['bitis'];
        $_data['zemin_rengi'] = $v['zemin_rengi'];
        $_data['tanim'] = $v['tanim'];
        $_data['round_bitis'] = $v['round_bitis'];
        $_data['rowspan_row'] = $v['rowspan_row'];
        $_data['rowspan'] = $v['rowspan'];
        $_data['baslangic_round'] = $v['baslangic_round'];

        $_data['derinlik_aciklama'] = $v['round_bitis'] . ' m';

        fn_insert_array('zemin_tanimi_for_log', $_data);
    }

    return get_zemin_tanimi_to_log($rapor_id, $secili_sk);
    // return $render;
}
// 16 Aralik 2024 15:27 @Workinton
// CNMAR mikro sunucu bekliyor.
// ritim Fatura bekleniyor.
// Barla sunucu bekliyor.
// Asi suporiz bekliyor (dyson)
/**

=> SPT
    20'dan az ise =>  Az kelimesi gececek.


10 Nolu Elek: (Cakil)
200 Nolu Elekten Gecen (Kil)
    50'den fazla ise => Siltli Kil - (EN SON DA) (Eger sonda kalmiyorsa sadece,Killi)

100  - (200 Elek + 10 Elek): (Kum)


==> Karot:
    Kirecli

 *
 */
function zemin_tanimlamasi_karot($elek10, $elek200)
{
    return 'Kireç Taşı';
}
function zemin_tanimlamasi_spt($elek10, $elek200, $likit_limit)
{
    $elekler = array();
    $elek10 = floatval(str_replace(',', '.', $elek10));
    $elek200 = floatval(str_replace(',', '.', $elek200));
    $elekler['cakil'] = $elek10;
    $elekler['kil'] = $elek200;
    $elekler['kum'] = floatval(100 - ($elek200 + $elek10));
    // pe($elekler);
    asort($elekler);

    $str = array();
    foreach ($elekler as $elek => $v) {
        $str[$elek] = zemin_tanim_text($elek, $v);
    }
    end($str);
    $son_sati = key($str);
    if ($son_sati == 'kil') {
        $str['kil'] = 'Siltli Kil';
    }
    if ($son_sati == 'kum' or $son_sati == 'cakil') {
        $str[$son_sati] = substr($str[$son_sati], 0, -2);;
    }

    if ($likit_limit == 'NP') {
        $str['kil'] = 'Siltli';
    }
    return implode(', ', $str);
}
function zemin_tanim_text($val, $sayi)
{

    if ($val == 'cakil') return ($sayi < 20) ? 'Az ' . 'Çakıllı' : 'Çakıllı';
    if ($val == 'kil') return ($sayi < 20) ? 'Az ' . 'Killi' : 'Killi';
    if ($val == 'kum') return ($sayi < 20) ? 'Az ' . 'Kumlu' : 'Kumlu';
}
function fn_rapor_sil($id)
{

    // Stklari sil
    fn_delete_row('sklar', "rapor_id=$id");
    // Log'lari sil
    fn_delete_row('log', "rapor_id=$id");
    // Yer alt sulari
    fn_delete_row('yeralti_sulari', "sk_id=$id");

    // Raporu sil
    fn_delete_row('raporlar', "id=$id");
}

function round_zemin($say)
{
    return round($say * 2) / 2;
}
