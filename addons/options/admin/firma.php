<?php

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    if ($_FILES) {

        foreach ($_FILES as $k => $v) {
            if ($_FILES[$k]['name']) {
                $images_dir = images_url($target);
                $data[$k] = fn_file_upload($_FILES[$k], $images_dir);
            }
        }
    }

    if (empty($id)) {
        $id = fn_insert_array('user_options', $data);
        fn_set_notice("Bilgiler eklendi.");
    }

    if ($id) {
        fn_update_array('user_options', $data, "id=$id limit 1");
        fn_set_notice("Bilgiler Güncellendi.");
    }


    fn_redirect("?do=$target/$mode");
}


$data = get_row("user_options", "user_id=" . USER_LOGIN);
$_cart_title = 'Firma Bilgileri';
$btn_action = _('Kaydet');
?>
<form class="form valid_form" method="POST" enctype="multipart/form-data">
    <input type="hidden" name="mode" value="<?php echo $mode ?>">
    <?php if (isset($data) && $data->id) : ?>
        <input type="hidden" name="id" value="<?php echo $data->id ?>">
    <?php endif ?>
    <input type="hidden" name="data[user_id]" value="<?php echo USER_LOGIN ?>">
    <div class="card card-custom">
        <?php include  COMMON_DIR . '/card/title.php'; ?>
        <div class="card-body">
            <?php
            form_separator('Firma Bilgileri', 'my-10', false);
            form_group_input_common('Firma Adı', 'firma_adi', 'required');
            form_group_input_image('Favicon', 'favicon', '', 'Tarayıcı sekmesinde bulunan logonun.');

            form_group_input_image('Logo', 'logo_light', 'required', 'Firmanızın logosu, mail, portal vb. yerlerde kullanılır.');
            form_group_input_image('Logo Minimize', 'logo_dark', '', 'Sidebar küçüldüğünde görünecek logonuz.');

            form_separator('İletişim Bilgileri', 'mt-10 mb-10');
            form_group_input_common('Adresiniz', 'adress', 'required', 'Şirket adresiniz.');
            form_group_input_common('Telefon Numarası', 'telefon', 'required', 'Kullanıcıların sizinle iletişime geçebileceği bir telefon numarası');
            form_group_input_mail('E-Posta', 'email', 'required', 'Sistem üzerinden tüm mailler bu adrese yönlendirilecektir');
            ?>
        </div>
        <?php include  COMMON_DIR . '/card/footer.php';  ?>
    </div>
</form>