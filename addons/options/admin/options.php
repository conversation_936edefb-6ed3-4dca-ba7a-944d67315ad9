<?php
if ($mode == 'get_ilce') {
    $iller = get_results('ilce', "il_id=$il_id");
    $options = '<option></option>';
    foreach ($iller as $v) {
        $options .= '<option value="' . $v->ad . '">' . $v->ad . '</option>';
    }
    $r = array();
    $r['options'] = $options;
    send_json($r);
}
if ($mode == 'logout') {

    fn_logout();
    fn_set_notice(_('Güvenli çıkış başarılı'));
    fn_redirect(WEB_DIR);
}
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    if ($mode == 'delete') {

        fn_delete_user($id);
        $data = array();
        send_json($data);
    }
}


include(ADDONS_DIR . '/' . $target . '/admin/' . $mode . '.php');
