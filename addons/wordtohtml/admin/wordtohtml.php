<?php
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Settings;
use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Element\Text;
use PhpOffice\PhpWord\Element\TextRun;
use PhpOffice\PhpWord\Element\Title;
use PhpOffice\PhpWord\Element\ListItemRun;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\Element\PreserveText;

if ($mode == 'upload') {
    ini_set('memory_limit', '512M');
    ini_set('max_execution_time', 300);

    try {
        $vendorDirPath = realpath(LIB_DIR . DS . 'phpword' . '/vendor');
        require $vendorDirPath . '/autoload.php';

        $uploadPath = DIR_ROOT . '/addons/wordtohtml/files/sample.docx';

        if (file_exists($uploadPath)) {
            // Önceki içeriği temizle
            $db->query("DELETE FROM wordtohtml WHERE original_file = 'sample.docx'");

            $phpWord = IOFactory::load($uploadPath);
            $sections = $phpWord->getSections();

            foreach ($sections as $sectionIndex => $section) {
                $elements = $section->getElements();
                $currentHeading1 = '';
                $currentHeading2 = '';
                $currentSubtitle = '';
                $currentContent = '';
                $skipTOC = false;

                foreach ($elements as $elementIndex => $element) {
                    // İçindekiler tablosunu atla
                    if ($element instanceof TextRun) {
                        $style = $element->getParagraphStyle();
                        $styleName = $style ? $style->getStyleName() : '';
                        if (strpos($styleName, 'TOC') !== false) {
                            $skipTOC = true;
                            continue;
                        }
                    }

                    // Başlık kontrolü
                    if ($element instanceof ListItemRun) {
                        $style = $element->getParagraphStyle();
                        $styleName = $style ? $style->getStyleName() : '';
                        $text = getElementText($element);

                        if (strpos($styleName, 'Heading1') !== false) {
                            // Önceki içeriği kaydet
                            saveCurrentContent($currentHeading1, $currentHeading2, $currentSubtitle, $currentContent, $sectionIndex);

                            // Yeni başlık
                            $currentHeading1 = $text;
                            $currentHeading2 = '';
                            $currentSubtitle = '';
                            $currentContent = '';
                        } elseif (strpos($styleName, 'Heading2') !== false) {
                            // Önceki içeriği kaydet
                            saveCurrentContent($currentHeading1, $currentHeading2, $currentSubtitle, $currentContent, $sectionIndex);

                            // Yeni alt başlık
                            $currentHeading2 = $text;
                            $currentSubtitle = '';
                            $currentContent = '';
                        } elseif (strpos($styleName, 'Subtitle') !== false) {
                            // Önceki içeriği kaydet
                            saveCurrentContent($currentHeading1, $currentHeading2, $currentSubtitle, $currentContent, $sectionIndex);

                            // Yeni alt alt başlık
                            $currentSubtitle = $text;
                            $currentContent = '';
                        } else {
                            // Normal liste içeriği
                            $currentContent .= $text . "\n";
                        }
                    }
                    // İçerik kontrolü
                    elseif ($element instanceof TextRun && !$skipTOC) {
                        $text = getElementText($element);
                        if (!empty($text)) {
                            $currentContent .= $text . "\n";
                        }
                    }
                    // Tablo kontrolü
                    elseif ($element instanceof Table && !$skipTOC) {
                        $currentContent .= getTableContent($element) . "\n";
                    }
                }

                // Son içeriği kaydet
                saveCurrentContent($currentHeading1, $currentHeading2, $currentSubtitle, $currentContent, $sectionIndex);
            }

            fn_set_notice('Başlıklar başarıyla ayrıştırıldı ve kaydedildi.');
            fn_redirect(fn_menu_link('wordtohtml/manage', 1));
        }
    } catch (Exception $e) {
        fn_set_notice('Hata: ' . $e->getMessage(), 'E');
        fn_redirect(fn_menu_link('wordtohtml/manage', 1));
    }
}

function getElementText($element)
{
    $text = '';
    if ($element instanceof ListItemRun || $element instanceof TextRun) {
        foreach ($element->getElements() as $textElement) {
            if ($textElement instanceof Text) {
                $text .= $textElement->getText();
            }
        }
    }
    return trim($text);
}

function getTableContent($table)
{
    $content = '';
    foreach ($table->getRows() as $row) {
        foreach ($row->getCells() as $cell) {
            foreach ($cell->getElements() as $element) {
                $content .= getElementText($element) . "\t";
            }
        }
        $content .= "\n";
    }
    return $content;
}

function saveCurrentContent($heading1, $heading2, $subtitle, $content, $sectionIndex)
{
    if (empty($heading1) && empty($heading2) && empty($subtitle)) {
        return;
    }

    $title = trim($heading1);
    if (!empty($heading2)) {
        $title = trim($heading2);
    }
    if (!empty($subtitle)) {
        $title = trim($subtitle);
    }

    if (!empty($title)) {
        $data = array(
            'title' => $title,
            'content' => htmlspecialchars(trim($content), ENT_QUOTES, 'UTF-8'),
            'page_number' => $sectionIndex + 1,
            'original_file' => 'sample.docx',
            'heading1' => $heading1,
            'heading2' => $heading2,
            'subtitle' => $subtitle
        );
        fn_insert_array('wordtohtml', $data);
    }
}

include(ADDONS_DIR . '/wordtohtml/admin/' . $mode . '.php');