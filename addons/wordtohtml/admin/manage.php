<?php if (!defined('AREA'))
    die('Access denied'); ?>

<div class="card card-flush">
    <div class="card-header">
        <h3 class="card-title">Word to HTML Dönüştürücü</h3>
    </div>

    <div class="card-body">
        <!-- Dönüştürme Butonu -->
        <form action="<?php echo fn_menu_link('wordtohtml/upload'); ?>" method="post">
            <div class="row mb-5">
                <div class="col-md-6">
                    <button type="submit" class="btn btn-primary">sample.doc <PERSON>nı Dönüştür</button>
                </div>
            </div>
        </form>

        <!-- Dönüştürülmüş İçerik Ağacı -->
        <div class="mt-5">
            <?php
            $documents = $db->get_results("
                SELECT * FROM wordtohtml 
                WHERE is_delete = 0 
                ORDER BY page_number ASC, id ASC
            ");

            if ($documents):
                $currentPage = 0;
                foreach ($documents as $doc):
                    // Yeni sayfa başlangıcı
                    if ($currentPage != $doc->page_number):
                        if ($currentPage != 0)
                            echo '</div>'; // Önceki sayfayı kapat
                        $currentPage = $doc->page_number;
                        ?>
                        <div class="accordion mb-5" id="page_<?php echo $currentPage; ?>">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse_<?php echo $currentPage; ?>">
                                        Sayfa <?php echo $currentPage; ?>
                                    </button>
                                </h2>
                                <div id="collapse_<?php echo $currentPage; ?>" class="accordion-collapse collapse show">
                                    <div class="accordion-body">
                                        <?php
                    endif;

                    // Başlık tipini belirle
                    $titleClass = '';
                    if (strpos($doc->title, 'Heading1') !== false) {
                        $titleClass = 'fs-3 fw-bold mb-2 mt-4';
                    } elseif (strpos($doc->title, 'Heading2') !== false) {
                        $titleClass = 'fs-4 fw-semibold mb-2 mt-3 ps-4';
                    } elseif (strpos($doc->title, 'Subtitle') !== false) {
                        $titleClass = 'fs-5 mb-2 mt-2 ps-8';
                    }
                    ?>

                                    <div class="border-bottom pb-3">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <h3 class="<?php echo $titleClass; ?>"><?php echo $doc->title; ?></h3>
                                            <a href="<?php echo fn_menu_link('wordtohtml/view&id=' . $doc->id); ?>"
                                                class="btn btn-sm btn-light btn-active-light-primary">
                                                Görüntüle
                                            </a>
                                        </div>
                                        <?php if (!empty($doc->content)): ?>
                                            <div class="text-gray-600 ps-4 pt-2">
                                                <?php echo nl2br(substr($doc->content, 0, 200)) . (strlen($doc->content) > 200 ? '...' : ''); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <?php
                endforeach;
                if ($currentPage != 0)
                    echo '</div></div></div>'; // Son sayfayı kapat
            endif;
            ?>
                        </div>
                    </div>
                </div>

                <!-- Bootstrap Accordion için gerekli JS -->
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        // Tüm accordionları açık tut
                        var accordions = document.querySelectorAll('.accordion-collapse');
                        accordions.forEach(function (accordion) {
                            accordion.classList.add('show');
                        });
                    });
                </script>