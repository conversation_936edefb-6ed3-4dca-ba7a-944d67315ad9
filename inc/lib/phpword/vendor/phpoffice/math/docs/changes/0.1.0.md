# 0.1.0

## Enhancements

- Initial version by [@Progi1984](https://github/Progi1984)
- MathML Reader : Support for Semantics by [@Progi1984](https://github/Progi1984) in [#4](https://github.com/PHPOffice/Math/pull/4)
- PHPUnit : Improved Unit Tests by [@Progi1984](https://github/Progi1984) in [#8](https://github.com/PHPOffice/Math/pull/8)

## Bug fixes

- N/A

## Miscellaneous
- Github Actions : PHPCSFixer by [@Progi1984](https://github/Progi1984) in [#1](https://github.com/PHPOffice/Math/pull/1)
- Github Actions : PHPStan by [@Progi1984](https://github/Progi1984) in [#2](https://github.com/PHPOffice/Math/pull/2)
- Removed dependency friendsofphp/php-cs-fixer by [@Progi1984](https://github/Progi1984) in [#3](https://github.com/PHPOffice/Math/pull/3)
- Github Actions : Dependabot by [@Progi1984](https://github/Progi1984) in [#5](https://github.com/PHPOffice/Math/pull/5)
- Bump actions/checkout from 2 to 4 by [@dependabot](https://github/dependabot) in [#6](https://github.com/PHPOffice/Math/pull/6)
- Added documentation (MkDocs / Coverage / PHPDoc) by [@Progi1984](https://github/Progi1984) in [#7](https://github.com/PHPOffice/Math/pull/7)