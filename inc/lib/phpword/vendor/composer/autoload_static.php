<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit85eda1acc5885e89f37aed67466a74ba
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PhpOffice\\PhpWord\\' => 18,
            'PhpOffice\\Math\\' => 15,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PhpOffice\\PhpWord\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpoffice/phpword/src/PhpWord',
        ),
        'PhpOffice\\Math\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpoffice/math/src/Math',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit85eda1acc5885e89f37aed67466a74ba::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit85eda1acc5885e89f37aed67466a74ba::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit85eda1acc5885e89f37aed67466a74ba::$classMap;

        }, null, ClassLoader::class);
    }
}
