<?php

use <PERSON><PERSON><PERSON><PERSON>er\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

function mai($kime, $subject, $metin)
{
} //end function

function fn_SendMail($kime, $subject, $metin)
{
	global $options, $MAIL_HEADER, $MAIL_BODY_BEFORE, $MAIL_BODY_AFTER;

	$metin = $MAIL_HEADER . $MAIL_BODY_BEFORE . $metin . $MAIL_BODY_AFTER;
	$mail = new PHPMailer(true);
	try {
		//Server settings
		$mail->CharSet = 'UTF-8';
		$mail->SMTPDebug = 0; // debug on - off
		$mail->isSMTP();
		$mail->Host = $options['Mail']['host']; // SMTP sunucusu örnek : mail.alanadi.com
		$mail->SMTPAuth = true; // SMTP Doğrulama
		$mail->Username = $options['Mail']['username']; // Mail kullanıcı adı
		$mail->Password = $options['Mail']['password']; // Mail şifresi
		$mail->SMTPSecure = 'tls'; // Şifreleme
		$mail->Port = 587; // SMTP Port
		$mail->SMTPOptions = array(
			'ssl' => array(
				'verify_peer' => false,
				'verify_peer_name' => false,
				'allow_self_signed' => true
			)
		);

		//Alıcılar
		$mail->setfrom($options['Mail']['username'], $options['General']['firma']);
		$mail->addAddress($kime);

		//Recipients
		// $mail->setFrom('<EMAIL>', 'Mailer');
		// $mail->addAddress('<EMAIL>', 'Joe User');     //Add a recipient
		// $mail->addAddress('<EMAIL>');               //Name is optional
		// $mail->addReplyTo('<EMAIL>', 'Information');
		// $mail->addCC('<EMAIL>');
		// $mail->addBCC('<EMAIL>');
		$mail->addReplyTo($options['Mail']['iletisim_mail'], $options['General']['firma']);


		//Attachments
		// $mail->addAttachment('/var/tmp/file.tar.gz');         //Add attachments
		// $mail->addAttachment('/tmp/image.jpg', 'new.jpg');    //Optional name

		//İçerik
		$mail->isHTML(true);
		$mail->Subject = $subject;
		$mail->Body = $metin;

		$mail->send();
		return true;
	} catch (Exception $e) {
		echo 'Mesajınız İletilemedi. Hata: ', $mail->ErrorInfo;
		return false;
	}
} //end function


//
/// Mail Gönder
//
function fn_mail_gonder($tip, $email, $data)
{
	global $options;

	switch ($tip) {
		case 'anket_cevap':


			$anket = get_row('anketler', "id=$data");
			$egitim = get_row('egitimler', "id=$anket->egitim_id");
			$musteri = get_row('users', "user_id=$anket->user_id");
			$cevaplar = get_results('anket_cevaplari', "anket_id=$data");
			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();
			$subject = _('Yeni Anket Formu');
			break;
		case 'sifremiunuttum':

			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();
			$subject = _('Şifre Talebi');
			break;
		case 'yeni-uye':

			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();
			$subject = _('Yeni Üyelik Bilgilendirme');
			break;
		case 'yeni-uye-dogrulama':
			$data = get_row('users', "user_id=$data");
			$email = $data->email;
			$onayla_link = WEB_DIR . 'index.php?do=login/onay&secret_id=' . $data->secret_code . '&email=' . $data->email;
			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();
			$subject = _('Yeni Üyelik Onayı');
			break;
		case 'yeni-uye-admin':
			$email = $options['Mail']['yeni_uye'];

			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();

			$subject = _('Yeni Üyelik Bilgilendirme');
			break;
		case 'iletisim':

			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();
			$subject = _('Yeni bir mesaj var');
			break;
		case 'yeni-davetiye':
			$HTML = time();
			$data = get_row('users', "user_id=$data");

			ob_start();
			include(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_contents();
			ob_clean();
			ob_flush();
			flush();

			$subject = _('Eğitime Davetlisiniz');
			break;

		case 'yeni-siparis':
			$email = $options['Mail']['iletisim_mail'];
			$order = get_row('orders', "id=$data");
			$order_products = get_results('order_products', "order_id=$order->id");

			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();
			$subject = _('Yeni Sipariş Bilgilendirme');
			break;
		case 'yeni-siparis-musteri':

			$order = get_row('orders', "id=$data");
			$order_products = get_results('order_products', "order_id=$order->id");
			$user = get_row('users', "user_id=$order->user_id");
			$email = $user->email;
			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();
			$subject = _('Yeni Sipariş Bilgilendirme');
			break;
		case 'bildirim_60_gun':
		case 'bildirim_45_gun':
		case 'bildirim_15_gun':
		case 'bildirim_5_gun':

			list($a, $period, $b) = explode('_', $tip);

			$sinav = get_row('egitim_sinav', "id=$data");
			$user = get_row('users', "user_id=$sinav->user_id");
			$email = $user->email;

			$email = $options['Mail']['iletisim_mail'];
			ob_start();
			require_once(COMMON_DIR . "/mail/$tip.php");
			$HTML = ob_get_clean();
			$subject = 'Eğitimi Bitirmenize Son ' . $period . ' Gün';
			break;
	}


	return fn_SendMail($email, $subject, $HTML);
}
