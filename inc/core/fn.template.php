<?php
// 18 Aralik 2024 11:07 @workinton
// Jeotek icin
// Arin babasi oldu! Bugun konser var, melike

function question_toooltip($text)
{
    echo '<span class="fw-bold" data-container="body" data-bs-toggle="tooltip" data-placement="top" title="' . $text . '"><i class="fa-solid fs-5 fa-circle-question"></i></span>';
}
// 14 Kasim 2024 16:31 @ozluceHome
// Jeotek Platformu icin
// Arin babasi entube
function form_select_users($title, $data_name, $where = "1=1", $is_required = '', $desc = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="text-muted fs-7">' . $desc . '</div>';
    }
    $user = get_results('users', $where);
    $options = '<option></option>';
    foreach ($user as $v) {
        $selected = ($v->user_id == $data_value) ? 'selected="selected"' : '';
        $oname = $v->name . ' ' . $v->lastname;
        $oname .= ($v->firma_adi) ? ' - ' . $v->firma_adi : '';
        $options .= '<option ' . $selected . ' value="' . $v->user_id . '">' . $oname . '</option>';
    }

    $html = <<<"EOT"
    <div class="mb-4 fv-row">
        <label class=" form-label $class">$title</label>
          <select class="form-select" data-control="select2" data-placeholder="Seçim yapınız" name="data[$data_name]" id="data_$data_name" $required>
            $options
            </select>
        $desc_html
    </div>
EOT;
    echo $html;
}
// 14 Kasim 2024 16:31 @ozluceHome
// Jeotek Platformu icin
// Arin babasi entube
function form_select($title, $data_name, $datas = array(), $is_required = '', $hide_search = false, $desc = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="text-muted fs-7">' . $desc . '</div>';
    }
    $options = '<option></option>';
    foreach ($datas as $v) {
        $selected = ($v == $data_value) ? 'selected="selected"' : '';
        $options .= '<option ' . $selected . ' value="' . $v . '">' . $v . '</option>';
    }
    $attr = '';
    if ($hide_search) {
        $attr = 'data-hide-search="true"';
    }

    $html = <<<"EOT"
    <div class="mb-4 fv-row">
        <label class=" form-label $class">$title</label>
          <select class="form-select" data-control="select2" $attr data-placeholder="Seçim yapınız" name="data[$data_name]" id="data_$data_name" $required>
            $options
            </select>
        $desc_html
    </div>
EOT;
    echo $html;
}

function form_ilceler($title, $data_name, $is_required = '', $desc = '')
{
    global $data;
    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
        $il_id = $data->il_id;
    }

    //Required
    $class_required = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class_required = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="text-muted fs-7">' . $class_required . '</div>';
    }

    $iller = get_results('ilce', "il_id=$il_id");
    $options = '<option></option>';
    foreach ($iller as $v) {
        $selected = ($v->ad == $data_value) ? 'selected="selected"' : '';
        $options .= '<option ' . $selected . ' value="' . $v->ad . '">' . $v->ad . '</option>';
    }

    $html = <<<"EOT"
 <div class="mb-4 fv-row">
     <label class="form-label $class_required">$title</label>
     <select class="form-select" data-control="select2" data-placeholder="Seçim yapınız" name="data[$data_name]" id="data_$data_name" $required>
     $options
     </select>
     $desc_html
 </div>
EOT;
    echo $html;
}
function form_iller($title, $data_name, $is_required = '', $desc = '')
{
    global $data;
    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class_required = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class_required = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="text-muted fs-7">' . $class_required . '</div>';
    }

    $iller = get_results('il');
    $options = '<option></option>';
    $il_str = '';
    foreach ($iller as $v) {
        $selected =  '';
        if (($v->id == $data_value)) {
            $selected =  'selected="selected"';
            $il_str = '<input type="hidden" id="data_il_adi" name="data[il]" value="' . $v->ad . '"/>';
        }
        $options .= '<option ' . $selected . ' data-adi="' . $v->ad . '" value="' . $v->id . '">' . $v->ad . '</option>';
    }

    $html = <<<"EOT"
 <div class="mb-4 fv-row">
     <label class="form-label $class_required">$title</label>
     <select class="form-select" data-control="select2" data-placeholder="Seçim yapınız" name="data[$data_name]" id="data_$data_name" $required>
     $options
     </select>
     $il_str
     $desc_html
 </div>
EOT;
    echo $html;
}
function form_ckeditor($title, $data_name, $is_required = '', $desc = '')
{
    form_textarea($title, $data_name, $is_required, $desc, 'evockeditor');
}
function form_textarea($title, $data_name, $is_required = '', $desc = '', $class = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class_required = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class_required = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="text-muted fs-7">' . $class_required . '</div>';
    }
    //data-kt-autosize="true"
    $html = <<<"EOT"
    <div class="mb-4 fv-row">
        <label class=" form-label $class_required">$title</label>
        <textarea  name="data[$data_name]" class="form-control mb-2 $class" id="data_$data_name" placeholder="" $required>$data_value</textarea>
        $desc_html
    </div>
EOT;
    echo $html;
}

// 14 Kasim 2024 16:31 @ozluceHome
// Jeotek Platformu icin
// Arin babasi entube
function form_input_group($title, $data_name, $text = "", $is_required = '', $desc = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="text-muted fs-7">' . $desc . '</div>';
    }
    $html = <<<"EOT"
    <div class="mb-4 fv-row">
        <label class=" form-label $class">$title</label>
        <div class="input-group mb-5">
        <input type="text" name="data[$data_name]" class="form-control" placeholder="" value="$data_value" $required>
        <span class="input-group-text">$text</span>
        </div>
        $desc_html
    </div>
EOT;
    echo $html;
}
// 6 Kasim 2024 10:38 @workinton
// Online platform icin
function form_input($title, $data_name, $is_required = '', $desc = '', $ex_class = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="text-muted fs-7">' . $desc . '</div>';
    }
    $html = <<<"EOT"
    <div class="mb-4 fv-row">
        <label class="form-label $class">$title</label>
        <input type="text" name="data[$data_name]" class="form-control $ex_class" placeholder="" autocomplete="on" value="$data_value" $required>
        $desc_html
    </div>
EOT;
    echo $html;
}
function string_array_category($data)
{
    if (empty($data)) return '';
    $str = array();
    foreach ($data as $k => $s) {
        if (!fn_get_var('category', "", $s['relationships_id'])) continue;

        $str[] = fn_get_var('category', "", $s['relationships_id']);
    }
    return implode(', ', $str);
}
function label_array_category($data)
{
    if (empty($data)) return '';
    $str = '';
    foreach ($data as $k => $s) {
        if (!fn_get_var('category', "", $s['relationships_id'])) continue;

        $str .= '<span class="badge badge-light me-2">' . fn_get_var('category', "", $s['relationships_id'])  . '</span>';
    }
    return $str;
}

//
/// INPUT CKEDITOR 
//
function form_group_ckeditor($title, $data_name, $is_required = '', $desc = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }
    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-lg-3 form-label required">$title</label>
        <div class="col-lg-9">
            <textarea name="data[$data_name]"  class="evockeditor" $required>$data_value</textarea>
            $desc_html
        </div>
    </div>
EOT;



    echo $html;
}

//
/// INPUT COMMON 
//
function form_group_input_fiyat($title, $data_name, $is_required = '', $desc = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }


    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label required">$title</label>
        <div class="col-lg-6 col-xl-3">
            <div class="input-group">
                <input type="text" name="data[$data_name]"  class="form-control money" value="$data_value"  placeholder="" $required aria-describedby="basic-addon2">
                 <span class="input-group-text">TL</span>
            </div>
            $desc_html
        </div>
    </div>
EOT;

    echo $html;
}

//
/// FILE INPUT IMAGE 
//
function form_group_input_image($title, $data_name, $is_required = '', $desc = '', $image_url = false)
{
    global $data, $target;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class_label = $required = '';
    if ($is_required) {
        if (empty($data_value))
            $required = 'required="required"';
        $class_label = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="form-text text-muted">' . $desc . '</div>';
    }


    //Disabled
    $image_full_url = '';
    if (!$image_url) {
        $image_url = images_url($target, 'url');
    }
    if ($data_value) {
        $image_full_t = T . $image_url . $data_value . '&w=220';
        $image_full_url = $image_url . $data_value;
        $image_row_html = '<div class="col flex-center d-flex">
            <a href="' . $image_full_url . '" target="_blank"><img src="' . $image_full_t . '" class="img-fluid mb-6 rounded" alt="' . $title . '" /></a>
        </div>';
    }
    $html = <<<"EOT"
    <input type="hidden" name="before_avatar" value="$data_value" />    
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label $class_label">$title</label>
        <div class="col-lg-9 col-xl-6">
        <div class="row">
            <div class="col">
                <input class="form-control" name="$data_name" accept=".png, .jpg, .jpeg, .svg" type="file" $required>
                $desc_html
            </div>
            $image_row_html
        </div>
        </div>
    </div>
EOT;

    echo $html;
}

function the_row_field($title, $value)
{
    global $data;
    echo '<div class="row mb-7">
<label class="col-lg-4 fw-semibold text-muted">' . $title . '</label>
<div class="col-lg-8">
    <span class="fw-bold fs-6 text-gray-800">' . $data->$value . '</span>
</div>
</div>';
}
//
/// 12 Subat 2021 13:52
/// CNMAr projesi ve yepyeni dongu icin altyapi
// @worqzone


//
/// TABLE ACTION ROW 
//
function table_action_row($_data, $edit_link_ar = '', $delete_link_ar = '')
{
    global $target, $edit_link, $delete_link;

    if ($edit_link_ar) $edit_link = $edit_link_ar;
    if ($delete_link_ar) $delete_link = $delete_link_ar;

    if (is_object($_data)) {

        $data = $_data;
    } else {
        $data = new stdClass();
        $data->id = $_data;
    }

    $delete_link_a = $edit_linkt_a = '';
    if ($edit_link != 'none') {


        if ($edit_link_ar) {
            $edit_link = $edit_link_ar;
        } else {
            $edit_link = fn_menu_link($target . '/update&id=' . $data->id, 1);
        }
        $str_duzenle = _('Bilgileri Düzenle');
        $edit_linkt_a =  <<<"EOT"
        <a href="$edit_link" class="btn btn-sm btn-secondary btn-text-primary btn-icon me-1" data-toggle="tooltip" data-placement="top" data-container="body" data-boundary="window" title="$str_duzenle"><i class="ki-duotone ki-pencil fs-3"><span class="path1"></span><span class="path2"></span></i></a>
EOT;
    }
    if ($delete_link != 'none') {

        if ($delete_link_ar) {
            $delete_link = $delete_link_ar;
        } else {
            $delete_link = fn_menu_link($target . '/delete&id=' . $data->id, 1);
        }
        $str_sil = _('Bu Satırı Sil');
        $delete_link_a = <<<"EOT"
            <a href="$delete_link" data-id="$data->id" class="btn btn-sm btn-secondary btn-text-primary btn-icon delete-table-row" data-toggle="tooltip" data-placement="top" data-container="body" data-boundary="window" title="$str_sil"><i class="ki-outline ki-cross fs-3"></i></a>
EOT;
    }

    // $edit_link = $delete_link = '';

    echo $edit_linkt_a . $delete_link_a;
}

//
/// INPUT DECIMAL - price 
//
function form_group_input_decimal($title, $data_name, $is_required = '', $desc = '')
{
    global $data;

    // Value
    $data_value = '';
    if ($data->$data_name) {
        $data_value = $data->$data_name;
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label required">$title</label>
        <div class="col-lg-9 col-xl-6">
            <input type="text" name="data[$data_name]" class="form-control money" value="$data_value" placeholder="" $required />
            $desc_html
        </div>
    </div>
EOT;

    echo $html;
}
//
/// INPUT PASS 
//
function form_group_input_pass($title, $data_name, $is_required = '', $desc = '')
{

    global $data;
    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class_label = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class_label = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }

    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label $class_label">$title</label>
        <div class="col-lg-9 col-xl-6">
            <div class="input-group mb-2">
            <span class="input-group-text"><i class="fa-solid fa-lock" aria-hidden="true"></i></span>
                <input type="password" name="data[$data_name]" class="form-control" value="$data_value" placeholder="" $required  />
            </div>
            $desc_html
        </div>
    </div>
EOT;

    echo $html;
}

//
/// INPUT COMMON 
//
function form_group_textarea($title, $data_name, $is_required = '', $desc = '', $is_disable = false)
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class_label = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class_label = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }


    //Disabled
    $disable = '';
    if ($is_disable) {
        $disable = 'disabled="disabled"';
    }

    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label $class_label">$title</label>
        <div class="col-lg-9 col-xl-6">
            <textarea autocomplete="off" data-kt-autosize="true" name="data[$data_name]" class="form-control mb-2" $required $disable>$data_value</textarea>
            $desc_html
        </div>
    </div>
EOT;

    echo $html;
}
//
/// INPUT INPUT GROUP 
//
function form_group_input_group($title, $data_name, $prefix, $is_required = '', $desc = '',)
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }



    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label">$title</label>
        <div class="col-lg-9 col-xl-6">
            <div class="input-group mb-5">
                <input type="text" name="data[$data_name]" class="form-control " value="$data_value"   $required />
                <span class="input-group-text">$prefix</span>
            </div>
            $desc_html
        </div>
    </div>
EOT;

    echo $html;
}
//
/// INPUT COMMON 
//
function form_group_input_common($title, $data_name, $is_required = '', $desc = '', $is_disable = false)
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class_label = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class_label = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="form-text text-muted">' . $desc . '</div>';
    }


    //Disabled
    $disable = '';
    if ($is_disable) {
        $disable = 'disabled="disabled"';
    }

    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label $class_label">$title</label>
        <div class="col-lg-9 col-xl-6">
            <input type="text"  name="data[$data_name]" class="form-control" value="$data_value" placeholder="" $required $disable />
            $desc_html
        </div>
    </div>
EOT;

    echo $html;
}
//
/// INPUT COLOR 
//
function form_group_input_color($title, $data_name, $is_required = '', $desc = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }

    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 col-form-label text-alert text-lg-right text-left">$title</label>
        <div class="col-lg-9 col-xl-6">
            <input type="text" autocomplete="off" name="data[$data_name]" class="form-control form-control-lg form-control-solid mb-2 colorpalet" value="$data_value" placeholder="" $required />
            $desc_html
        </div>
    </div>
EOT;

    echo $html;
}

//
/// INPUT RADIO 
//
function form_group_input_radio_no_col($data_name, $is_required = '', $table = '', $desc = '')
{
    global $data, $target;

    if (empty($table)) $table = $target;
    $enum_type = get_enum_values($table, $data_name);
    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }

    // Radios
    $labels = '';
    $s = 0;
    foreach ($enum_type as $k => $v) {
        $s++;
        $is_checked = ($v == $data_value) ? 'checked="checked"' : '';
        if ($s > 1) $required = '';

        $labels .= '<div class="form-check form-check-custom form-check-solid me-3">
        <input class="form-check-input" type="radio" name="data[' . $data_name . ']" ' . $is_checked . ' id="' . $data_name . '_' . $v . '" value="' . $v . '" ' . $required . '/>
        <label class="form-check-label fw-bold" for="' . $data_name . '_' . $v . '">
            ' . ucfirst($v) . '
        </label>
    </div>';
    }

    $html = <<<"EOT"
    <div class="d-flex mt-3">
    $labels
  </div>
EOT;

    echo $html;
}
//
/// INPUT RADIO 
//
function form_group_input_radio($title, $data_name, $is_required = '', $table = '', $desc = '')
{
    global $data, $target;

    if (empty($table)) $table = $target;
    $enum_type = get_enum_values($table, $data_name);
    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<div class="form-text text-muted fw-semibold">' . $desc . '</div>';
    }

    // Radios
    $labels = '';
    $s = 0;
    foreach ($enum_type as $k => $v) {
        $s++;
        $is_checked = ($v == $data_value) ? 'checked="checked"' : '';
        if ($s > 1) $required = '';
        $labels .= '<div class="form-check form-check-custom form-check-solid me-3">
        <input class="form-check-input" type="radio" name="data[' . $data_name . ']" ' . $is_checked . ' id="' . $data_name . '_' . $v . '" value="' . $v . '" ' . $required . '/>
        <label class="form-check-label fw-bold" for="' . $data_name . '_' . $v . '">
            ' . ucfirst($v) . '
        </label>
    </div>';
    }

    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 col-form-label text-alert text-lg-right text-left">$title</label>
        <div class="col-lg-9 col-xl-6">
         <div class="d-flex">
            $labels
          </div>
          $desc_html
        </div>
    </div>
EOT;

    echo $html;
}

//
/// INPUT PHONE
//
function form_group_input_number($title, $data_name, $is_required = '', $desc = '')
{
    global $data;

    // Value
    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }
    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }
    $row_id = fn_MakeSeo($data_name);

    $html = <<<"EOT"
      <div class="mb-6 row" id="data-row-$row_id">
        <label class="col-xl-3 col-lg-3 col-form-label text-alert text-lg-right text-left">$title</label>
        <div class="col-lg-9 col-xl-6">
            <div class="input-group input-group-solid">
                <div class="input-group-prepend">
                    <span class="input-group-text">
                    <i class="fas fa-sort-amount-up"></i>
                    </span>
                </div>
                <input type="number" name="data[$data_name]" class="form-control form-control-lg form-control-solid" value="$data_value" placeholder="" $required  />
                </div>
                $desc_html
        </div>
    </div>
EOT;

    echo $html;
}
//
/// INPUT PHONE
//
function form_group_input_phone($title, $data_name, $is_required = '')
{
    global $data;

    // Value
    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label">$title</label>
        <div class="col-lg-9 col-xl-6">
            <div class="input-group">
            <span class="input-group-text">
            <i class="fa-solid fa-phone"></i>
        </span>
                <input type="number" name="data[$data_name]" class="form-control " value="$data_value" placeholder="" $required  />
            </div>
        <span class="form-text text-muted">Herhangi bir boşluk olmadan, sadece rakkam olarak girniz. Örneğin: 00905304908454.</span>
        </div>
    </div>
EOT;

    echo $html;
}

//
/// INPUT EMAIL
//
function form_group_input_mail($title, $data_name, $is_required = '',  $desc = '')
{
    global $data;

    // Value
    $data_value = '';
    if (is_object($data) && $data->$data_name) {
        $data_value = $data->$data_name;
    }

    //Required
    $class_label = $required = '';
    if ($is_required) {
        $required = 'required="required"';
        $class_label = 'required';
    }

    // Desc
    $desc_html = '';
    if ($desc) {
        $desc_html = '<span class="form-text text-muted">' . $desc . '</span>';
    }

    $html = <<<"EOT"
      <div class="mb-6 row">
        <label class="col-xl-3 col-lg-3 form-label $class_label">$title</label>
        <div class="col-lg-9 col-xl-6">
            <div class="input-group mb-2">
            <span class="input-group-text"><i class="fa fa-envelope" aria-hidden="true"></i></span>
                <input type="email" name="data[$data_name]" class="form-control" value="$data_value" placeholder="" $required  />
            </div>
            $desc_html
        </div>
    </div>
EOT;

    echo $html;
}

//
/// INPUT EMAIL
//
function form_group_avatar($title, $data_name, $is_required = '', $image_url = '')
{
    global $s_w_tema, $data, $target;

    $defaul_value = COMMON_W_DIR . '/assets/img/blank-image.svg';
    $before_name = '';

    if (empty($image_url)) {
        $img_src = images_url($target, 'url');
    } else {
        $img_src = $image_url;
    }

    // Value
    if (is_object($data) &&  $data->$data_name) {
        $data_value = T . $img_src . $data->$data_name . '&h=300';
        $before_name = $data->$data_name;
    }
    if (empty($data_value)) {
        $data_value = $defaul_value;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    $html = <<<"EOT"
 <input type="hidden" name="before_$data_name"  value="$before_name" />
     <div class="mb-6 row">
            <label class="col-xl-3 col-lg-3 col-form-label text-lg-right text-left">$title</label>
            <div class="col-lg-9 col-xl-6">
            <!--begin::Image input-->
            <div class="image-input image-input-empty" data-kt-image-input="true" style="background-image: url($s_w_tema/assets/media/svg/avatars/blank.svg)">
                <!--begin::Image preview wrapper-->
                <div class="image-input-wrapper w-125px h-125px" style="background-image: url($data_value); "></div>
                <!--end::Image preview wrapper-->
        
                <!--begin::Edit button-->
                <label class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Resim seç">
                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>
        
                    <!--begin::Inputs-->
                    <input type="file" name="$data_name" accept=".png, .jpg, .jpeg" />
                    <input type="hidden" name="$data_name{remove}" />
                    <!--end::Inputs-->
                </label>
                <!--end::Edit button-->
        
                <!--begin::Cancel button-->
                <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Seçimi iptal et">
                    <i class="ki-outline ki-cross fs-3"></i>
                </span>
                <!--end::Cancel button-->
        
                <!--begin::Remove button-->
                <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Resimi kaldır">
                    <i class="ki-outline ki-cross fs-3"></i>
                </span>
                <!--end::Remove button-->
            </div>
            <!--end::Image input-->
            </div>
        </div>
EOT;

    echo $html;
}
// KRS ICIN
function form_group_avatar_krs($title, $data_name, $is_required = '', $avatar = '', $image_full = '')
{
    global $s_w_tema, $data;

    $defaul_value = $s_w_tema . '/assets/media/users/blank.png';
    // Value
    if (is_object($data) &&  $data->$data_name) {
        $data_value = T . $avatar . $data->$data_name . '&h=300';
    }
    if (empty($data_value)) {
        $data_value = $defaul_value;
    }
    if ($image_full) {
        $data_value = $image_full;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    $html = <<<"EOT"
 
     <div class="mb-6 row">
            <label class="col-xl-3 col-lg-3 col-form-label text-lg-right text-left">$title</label>
            <div class="col-lg-9 col-xl-6">
            <!--begin::Edit button-->
            <label class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar">
                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>
                <img src="{$data_value}" style="max-width: 120px; margin-right: 10px;" alt="">
                <input type="file" name="$data_name" accept=".png, .jpg, .jpeg" />
             
                <!--end::Inputs-->
            </label>
            <!--end::Edit button-->
            </div>
        </div>
EOT;

    echo $html;
}
//
// Separator
//
function form_separator($title, $margin = 'my-5', $sperator = true)
{
    if ($sperator) {
        $sperator_html = '<div class="separator  ' . $margin . '"></div>';
    } else {
        $sperator_html = '<div class="' . $margin . '"></div>';
    }

    $html = <<<"EOT"
    $sperator_html
        <div class="my-10">
            <h4 class="">$title:</h4>
        </div>
EOT;

    echo $html;
}

//
// Cotnact Listesi
//
function form_contact_list($owners)
{

?>
    <div id="kt_repeater_1">
        <div class="form-group form-group-last row">
            <label class="col-xl-3 col-lg-12 col-form-label text-lg-right text-left"></label>
            <div data-repeater-list="owners" class="col-lg-12 col-xl-6">
                <?php if (isset($owners)) : ?>
                    <?php foreach ($owners as $k => $v) : ?>
                        <div data-repeater-item class="mb-6 row align-items-center">
                            <div class="col-md-6 col-xl-6 mb-4">
                                <div class="kt-form__group--inline">
                                    <div class="kt-form__label">
                                        <label class="form-label"><?php echo _('Adı'); ?>:</label>
                                    </div>
                                    <div class="kt-form__control">
                                        <input type="text" autocomplete="off" class="form-control" name="adi" placeholder="" value="<?php echo $v->adi; ?>">
                                    </div>
                                </div>
                                <div class="d-md-none kt-margin-b-10"></div>
                            </div>
                            <div class="col-md-6 col-xl-6 mb-4">
                                <div class="kt-form__group--inline">
                                    <div class="kt-form__label">
                                        <label class="form-label"><?php echo _('Soyadı'); ?>:</label>
                                    </div>
                                    <div class="kt-form__control">
                                        <input type="text" autocomplete="off" class="form-control" name="soyadi" placeholder="" value="<?php echo $v->soyadi; ?>">
                                    </div>
                                </div>
                                <div class="d-md-none kt-margin-b-10"></div>
                            </div>
                            <div class="col-md-6 col-xl-6 mb-4">
                                <div class="kt-form__group--inline">
                                    <div class="kt-form__label">
                                        <label class="form-label"><?php echo _('E-posta'); ?>:</label>
                                    </div>
                                    <div class="kt-form__control">
                                        <input type="text" autocomplete="off" class="form-control" name="eposta" placeholder="" value="<?php echo $v->eposta; ?>">
                                    </div>
                                </div>
                                <div class="d-md-none kt-margin-b-10"></div>
                            </div>
                            <div class="col-md-6 col-xl-6">
                                <div class="kt-form__group--inline">
                                    <div class="kt-form__label">
                                        <label class="form-label"><?php echo _('Cep Telefonu'); ?>:</label>
                                    </div>
                                    <div class="kt-form__control">
                                        <input type="text" autocomplete="off" class="form-control" name="cep_telefonu" placeholder="" value="<?php echo $v->cep_telefonu; ?>">
                                    </div>
                                </div>
                                <div class="d-md-none kt-margin-b-10"></div>
                            </div>

                            <div class="col-md-2">
                                <a href="javascript:;" data-repeater-delete="" class="btn btn-sm btn-danger font-weight-bold mt-1">
                                    <i class="la la-trash-o"></i><?php echo _('Sil'); ?></a>
                            </div>
                        </div>
                    <?php endforeach ?>
                <?php endif ?>
            </div>
        </div>
        <div class="form-group form-group-last row">
            <label class="col-xl-3 col-lg-3 col-form-label text-lg-right text-left"></label>
            <div class="col-lg-9 col-xl-6">
                <a href="javascript:;" data-repeater-create="" class="btn btn-sm btn-light-success font-weight-bold"><i class="la la-plus"></i> <?php echo _('Ekle'); ?></a>
            </div>
        </div>
    </div>
<?php
}

//
// Separator
//
function form_group_image($title, $data_name, $is_required = false, $image_src = '', $desc = '')
{
    global $s_w_tema, $data, $target;


    $defaul_value = COMMON_W_DIR . '/assets/img/blank-img.jpg';
    // Value
    if (is_object($data) &&  $data->$data_name) {
        $data_value = $image_src . $data->$data_name;
    }
    if (empty($data_value)) {
        $data_value = $defaul_value;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    $icon = icon_get('delete');
    $icon_crop = icon_get('crop');

    $desc_alert = '';
    if ($desc) {
        $desc_alert = '<span class="alert alert-custom alert-notice alert-light-warning p-2 mb-0">' . $desc . '</span>';
    }

    $dosya_image_view = $dosya_varsa = '';
    if (is_object($data) && $data->$data_name) {
        $dosya_varsa = '<a href="#" data-field="' . $data_name . '" data-target="' . $target . '" data-id="' . $data->id . '" data-value="' . $data->$data_name . '" class="btn d-inline-block btn-sm btn-danger font-weight-bolder me-3 mb-3 btn-delete-file">' . $icon . ' Görseli Sil</a>';
        $dosya_image_view = '<div class="flex-shrink-0 me-5 mt-lg-0 mt-3">
                <a href="' . $data_value . '" target="_blank">
                    <div class="symbol symbol-50px symbol-lg-150px">
                        <img class="h-auto" src="' . $data_value . '" alt="image" id="image-' . $data_name . '" />
                    </div>
                </a>
            </div>';
    }
    $html = <<<"EOT"
    <div class="mb-6 row" id="content-$data_name">
        <label class="col-xl-3 col-lg-3 form-label">$title</label>
        <div class="col-lg-9 col-xl-6 d-flex">
            $dosya_image_view
            <div class="d-flex align-items-start justify-content-between flex-column py-2">
                    $dosya_varsa
                    <label class="btn btn-light-success btn-file-input"><span class="">Dosya Seç</span> <input type="file" name="$data_name" hidden $required></label>
                    $desc_alert
            </div>
        </div>
    </div>
EOT;

    echo $html;
}
function form_group_input_adres($title, $data_name)
{
    global $data;

    $secili_ulke = 'Turkey';
    $sehir = '';
    $adres = '';
    $adres2 = '';
    $postakodu = '';

    if (is_object($data)) {
        $sehir = $data->sehir;
        $secili_ulke = $data->ulke;
        $adres = $data->adres;
        $adres2 = $data->adres2;
        $postakodu = $data->postakodu;
    }

    $ulke_list = fn_ulke_list_option($secili_ulke);




    $html = <<<"EOT"
                <div class="row" id="content-$data_name">
                
                    <label class="col-xl-3"></label>
                    <div class="col-lg-12 col-xl-6 col-sm-12">
                        <!--begin::Row-->
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="form-group">
                                    <label class="form-label">Ülke</label>
                                    <select name="data[ulke]" class="form-control">
                                    $ulke_list
                                    </select>
                                </div>
                                <!--begin::Group-->
                            </div>
                            <!--end::Group-->
                            <!--begin::Group-->
                            <div class="col-xl-6">
                                <div class="form-group">
                                    <label class="form-label">Şehir</label>
                                    <input type="text" autocomplete="off" class="form-control" name="data[sehir]" placeholder="" value="$sehir" required="" />
                                    <span class="form-text text-muted"></span>
                                </div>
                            </div>
                            <!--end::Group-->
                        </div>
                        <!--end::Row-->
                        <!--begin::Row-->
                        <div class="row">
                            <div class="col-xl-6">
                                <!--begin::Group-->
                                <div class="form-group">
                                    <label class="form-label">Post Kodu</label>
                                    <input type="text" autocomplete="off" class="form-control" name="data[postakodu]" placeholder="" value="$postakodu" required="" />
                                    <span class="form-text text-muted"></span>
                                </div>
                                <!--end::Group-->
                            </div>
                            <div class="col-xl-6">
                            <div class="form-group">
                                <label class="form-label">Adres Satır 1</label>
                                <div class="input-group">
                                    <input type="text" autocomplete="off" class="form-control" name="data[adres]" placeholder="Adres Line 1" value="$adres" required="" />
                                    <span class="input-group-text">
                                    <i class="la la-map-marker"></i>
                                </span>
                                </div>
                            </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Adres Satır 2</label>
                            <input type="text" autocomplete="off" class="form-control" name="data[adres2]" placeholder="Adres Line 2" value="$adres2" />
                            <span class="form-text text-muted"></span>
                        </div>
    
                    </div>    
            </div>
EOT;

    echo $html;
}

function fn_ulke_list_option($selected_ulke)
{
    global $ulkeList_array;

    $str = '';
    foreach ($ulkeList_array as $k => $v) {
        $secili = ($v == $selected_ulke) ? 'selected="selected"' : '';
        $str .= '<option value="' . $v . '" ' . $secili . ' >' . $v . '</option>';
    }

    return $str;
}

function form_group_input_multiple($title, $data_name, $pattern = '', $desc = "")
{
    global $data;
    // Value
    $data_value = '';
    if (is_object($data) &&  $data->$data_name) {
        $data_value = $data->$data_name;
    }
    if ($desc) {
        $description = '<div class="mt-3 text-muted">' . $desc . '</div>';
    }
    $html = <<<"EOT"
            <div class="mb-6 row">
        <label class="form-label col-lg-3 col-sm-12">$title</label>
        <div class="col-lg-6 col-md-9 col-sm-12">
            <input id="tagify_1" class="form-control mb-2 tagify" name='data[$data_name]'  placeholder='type...' value='$data_value' data-blacklist='' pattern="$pattern" />
            <div class="mt-3">
                <a href="javascript:;" id="tagify_1_remove" class="btn btn-sm btn-light-danger font-weight-bold">Etiketleri kaldır</a>
            </div>
            $description
        </div>
    </div>
EOT;

    echo $html;
}
function label_array($data)
{
    if (empty($data)) return '';

    $_data = explode(',', $data);
    $str = '';
    foreach ($_data as $k => $v) {
        $str .= '<span class="mr-2" style="">' . $v . '</span>';
    }
    return $str;
}


function fn_datepicker_input($name, $is_required = false, $is_disable = false, $out_data = NULL)
{
    global $data;

    $defaul_value = '';

    if (is_object($out_data) && $out_data->$name) {
        $defaul_value = unix_to_date($out_data->$name, 2);
    } else {
        if (is_object($data) && $data->$name) {
            $defaul_value = unix_to_date($data->$name, 2);
        }
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    //Disabled
    $disable = '';
    if ($is_disable) {
        $disable = 'disabled="disabled"';
    }


    $html = <<<"EOT"
    <div class="input-group datetimepicker-evo" id="$name" data-td-target-input="nearest" data-td-target-toggle="nearest">
    <input name="data[$name]" id="input_$name" type="text" value="$defaul_value" $required $disable class="form-control data-mask" data-td-target="#$name"/>
    <span class="input-group-text" data-td-target="#$name" data-td-toggle="datetimepicker">
        <i class="ki-duotone ki-calendar fs-2"><span class="path1"></span><span class="path2"></span></i>
    </span>
</div>
EOT;
    echo $html;
}
function fn_datepicker_time_input($name, $is_required = false, $is_disable = false)
{
    global $data;

    $defaul_value = '';

    if (is_object($data) && $data->$name) {
        $defaul_value = unix_to_date($data->$name, 2);
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    //Disabled
    $disable = '';
    if ($is_disable) {
        $disable = 'disabled="disabled"';
    }

    $html = <<<"EOT"
    <div class="input-group datetimepicker-date-time-evo" id="$name" data-td-target-input="nearest" data-td-target-toggle="nearest">
    <input name="data[$name]" id="input_$name" type="text" value="$defaul_value" $required $disable class="form-control date-mask-time" data-td-target="#$name"/>
    <span class="input-group-text" data-td-target="#$name" data-td-toggle="datetimepicker">
        <i class="ki-duotone ki-calendar fs-2"><span class="path1"></span><span class="path2"></span></i>
    </span>
</div>
EOT;

    //     $html = <<<"EOT"
    // <div class="input-group datetimepicker-date-time-evo" id="$name" data-target-input="nearest">
    //         <input type="text" autocomplete="off" name="data[$name]" id="input_$name" class="form-control date-mask-time" placeholder="Select date &amp; time" data-target="#$name" value="$defaul_value" $required $disable autocomplete="off" />
    //         <div class="input-group-append" data-target="#$name" data-toggle="datetimepicker">
    //             <span class="input-group-text">
    //                 <i class="ki ki-calendar"></i>
    //             </span>
    //         </div>
    //     </div>
    // EOT;

    echo $html;
}
function fn_datepicker_input_time($name, $is_required = false, $is_disable = false)
{
    global $data;

    $defaul_name = $name;
    $name = $name . '_time';
    $defaul_value = '';

    if (is_object($data) && $data->$name) {
        // echo $defaul_value = unix_to_date($data->$name, 2);
        // 22 Mart 2022 17.15
        // Home: Deprem oldu
        $defaul_value = $data->$name;
    }

    //Required
    $required = '';
    if ($is_required) {
        $required = 'required="required"';
    }

    //Disabled
    $disable = '';
    if ($is_disable) {
        $disable = 'disabled="disabled"';
    }

    $html = <<<"EOT"
<div class="input-group datetimepicker-time-evo" id="$name" data-target-input="nearest">
        <input type="text" autocomplete="off" data-default="$defaul_name" name="data[$name]" id="input_$name" class="form-control bg-light date-mask-time" placeholder="Select time" data-target="#$name" value="$defaul_value" $required $disable />
        <span class="input-group-text">
        <i class="ki-duotone ki-timer fs-2">
 <i class="path1"></i>
 <i class="path2"></i>
 <i class="path3"></i>
</i></span>
    </div>
EOT;

    echo $html;
}
function fn_datepicker_input_pmam($name)
{
    global $data;

    $field_name = $name;
    $name = $name . '_period';
    $defaul_value = '';

    if (is_object($data) && $data->$name) {
        $defaul_value = $data->$name;
    }

    $bir_label = ($defaul_value == 'PM') ? 'active' : '';
    $bir_input = ($defaul_value == 'PM') ? 'checked' : '';

    $iki_label = ($defaul_value == 'AM') ? 'active' : '';
    $iki_input = ($defaul_value == 'AM') ? 'checked' : '';

    $uc_label = ($defaul_value == 'NIGTH') ? 'active' : '';
    $uc_input = ($defaul_value == 'NIGTH') ? 'checked' : '';

    $html = <<<"EOT"

<div class="btn-group btn-pm-am-group" role="group" id="$name" aria-label="Saat seçiniz">
  <input type="radio" class="btn-check" name="data[$name]" id="2_$name" autocomplete="off" value="AM" $iki_input>
  <label class="btn btn-light btn-active-color-success fw-bold $iki_label" data-parent="$name" data-field="$field_name" for="2_$name">AM</label>

  <label class="btn btn-light btn-active-color-success fw-bold $bir_label" data-parent="$name" data-field="$field_name" for="1_$name">PM</label>
  <input type="radio" class="btn-check" name="data[$name]" id="1_$name" autocomplete="off" value="PM"  $bir_input>
  
  <input type="radio" class="btn-check" name="data[$name]" id="3_$name" autocomplete="off" value="NIGTH" $uc_input>
  <label class="btn btn-light btn-active-color-success fw-bold $uc_label" data-parent="$name" data-field="$field_name" for="3_$name">NIGTH</label>
</div>
                                    
EOT;

    echo $html;
}
