<?php

//
/// 30 Agustos 2021 - Ritim Online
/// Worqzone
//
function fn_update_user($data, $id = 0)
{
    global $options;
    if ($id) {

        return fn_update_array('users', $data, "user_id=$id limit 1");
    } else {

        if (is_developer('v25'))
            $data['status'] = 2; // durum sms onayi bekliyor olarak ayarla.
        else
            $data['status'] = 0;
        $data['secret_code'] = md5(time());
        $user_id = fn_insert_array('users', $data);
        $user_data = get_row('users', 'user_id=' . $user_id);

        // Sms ile onayi cozuyoruz. Mail'e gere kalmadi.
        if (!is_developer('v25')) {
            // Istek
            fn_mail_gonder('yeni-uye-dogrulama', '', $user_id);
            fn_set_notice(_('Doğrulama maili gönderildi.'), 'W');
        }

        // $kime = '<EMAIL>';
        $kime = $options['Mail']['yeni_uye'];
        fn_mail_gonder('yeni-uye-admin', $kime, $user_data);
        // fn_set_notice(_('Mail Gönderildi.'), 'W');

        return $user_id;
    }
}

//
/// Worqzone 14 Eylul 2021 16:26
// Ritim icin
// Huzurluyum, daha iyisi icin neler mumkun? :)
function user_key($key)
{

    return @$_SESSION[strtolower(AREA)][$key];
}
function fn_login_after($user_id)
{
    global $profile_web_images, $employee_web_images;



    $user = get_row("users", "user_id=" . $user_id);
    if (AREA == 'A') {
        $_SESSION[strtolower(AREA)]['isletme_id'] = $user->isletme_id;
    }

    $uri_dir = (AREA == 'A') ? images_url('admin_users', 'url', $user->isletme_id)  : $profile_web_images;

    $_SESSION[strtolower(AREA)]['last_login'] = $user->last_login;
    $_SESSION[strtolower(AREA)]['user_login'] = $user->user_id;
    $_SESSION[strtolower(AREA)]['user_id'] = $user->user_id;
    $_SESSION[strtolower(AREA)]['name'] = $user->name;
    $_SESSION[strtolower(AREA)]['avatar'] = $user->avatar;

    $_SESSION[strtolower(AREA)]['pass'] = md5($user->pass);
    $_SESSION[strtolower(AREA)]['lastname'] = $user->lastname;
    $_SESSION[strtolower(AREA)]['namelastname'] = $user->name . ' ' . $user->lastname;
    $_SESSION[strtolower(AREA)]['user_type'] = $user->user_type;
    $_SESSION[strtolower(AREA)]['email'] = $user->email;
    $_SESSION[strtolower(AREA)]['gender'] = $user->gender;
    $_SESSION[strtolower(AREA)]['firma_adi'] = $user->firma_adi;


    if ($_SESSION[strtolower(AREA)]['avatar'])
        $profil_avatar = T . $uri_dir . $user->avatar . '&w=300&h=300';
    else
        $profil_avatar = COMMON_W_DIR . '/assets/img/blank-img.jpg';

    $_SESSION[strtolower(AREA)]['avatar_uri'] = $profil_avatar;




    $_data = array();
    $_data['last_login'] = time();
    fn_update_user($_data, $user_id);
}

function fn_logout()
{

    unset($_SESSION[strtolower(AREA)]['last_login']);
    unset($_SESSION[strtolower(AREA)]['user_login']);
    unset($_SESSION[strtolower(AREA)]['user_id']);
    unset($_SESSION[strtolower(AREA)]['name']);
    unset($_SESSION[strtolower(AREA)]['avatar']);

    unset($_SESSION[strtolower(AREA)]['lastname']);
    unset($_SESSION[strtolower(AREA)]['namelastname']);
    unset($_SESSION[strtolower(AREA)]['user_type']);
    unset($_SESSION[strtolower(AREA)]['email']);

    return true;
}

//
/// delete user [ Notice  : Silinmiyor. Durumu -is_delete- Y ]
//
function fn_delete_user($user_id)
{
    global $db;
    $db->query("UPDATE users SET is_delete = '1' WHERE user_id='$user_id' limit 1");
    return 1;
}
