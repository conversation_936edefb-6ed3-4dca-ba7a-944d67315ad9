<?php
function kayit_bulunamadi()
{

  echo '<div class="d-flex flex-center p-10">Kayıt bulunamadı</div>';
}

function fn_sef_link($slug, $page)
{

  if ($slug) {
    echo WEB_DIR . $slug . '/' . $page;
  } else {
    echo WEB_DIR . $page;
  }
}
//
/// Status String
//
function fn_status_common_txt($st)
{

  if ($st == 'A') echo 'Aktif';
  if ($st == 'H') echo 'Gizli';
  if ($st == 'D') echo 'Pasif';
  if ($st == 'T') echo 'Tükendi';
}

//
/// global object options ( active , hidden , disable )
//
function fn_status_common($name, $val = 'A', $tukendi = 0)
{
  if (empty($val)) $val = 'A'; ?>
  <div class="form-check form-check-custom form-check-solid me-4">
    <input class="form-check-input" type="radio" name="<?php echo $name; ?>[status]" <?php if ($val == 'A') echo 'checked="checked"'; ?> value="A" id="status_Aktif" />
    <label class="form-check-label" for="status_Aktif">
      Aktif
    </label>
  </div>
  <div class="form-check form-check-custom form-check-solid">
    <input class="form-check-input" type="radio" name="<?php echo $name; ?>[status]" <?php if ($val == 'D') echo 'checked="checked"'; ?> value="D" id="status_Gizli" />
    <label class="form-check-label" for="status_Gizli">
      Gizli
    </label>
  </div>



<?php }
function text_to_mode($mode)
{

  switch ($mode) {
    case 'manage':
      $str = 'Listesi';
      break;
    case 'add':
      $str = 'Yeni Ekle';
      break;
    case 'update':
      $str = 'Düzenle';
      break;
    case 'view':
      $str = 'Görüntüle';
      break;

    default:
      $str = ucfirst($mode);
      break;
  }

  return $str;
}
/**
 * 11 Mar 2011
 */

//
/// Clean Money From String
//
// @Workinton 9 Kasim 2022 16.33
function cleanMoneyFromStr($money)
{
  // remove everything except a digit "0-9", a comma ",", and a dot "."
  $money = preg_replace('/[^\d,\.]/', '', $money);

  // replace the comma with a dot, in the number format ",12" or ",43"
  $money = preg_replace('/,(\d{2})$/', '.$1', $money);

  if (empty($money)) return 0;
  return $money;
}

/// YENIDEN - 9 Subat 2021: 18.24 @Worqzone
//
function icon($target, $class = '', $return = 0)
{
  switch ($target) {
    case 'accordion':
      $icon = '
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <polygon points="0 0 24 0 24 24 0 24"></polygon>
              <path d="M12.2928955,6.70710318 C11.9023712,6.31657888 11.9023712,5.68341391 12.2928955,5.29288961 C12.6834198,4.90236532 13.3165848,4.90236532 13.7071091,5.29288961 L19.7071091,11.2928896 C20.085688,11.6714686 20.0989336,12.281055 19.7371564,12.675721 L14.2371564,18.675721 C13.863964,19.08284 13.2313966,19.1103429 12.8242777,18.7371505 C12.4171587,18.3639581 12.3896557,17.7313908 12.7628481,17.3242718 L17.6158645,12.0300721 L12.2928955,6.70710318 Z" fill="#000000" fill-rule="nonzero"></path>
              <path d="M3.70710678,15.7071068 C3.31658249,16.0976311 2.68341751,16.0976311 2.29289322,15.7071068 C1.90236893,15.3165825 1.90236893,14.6834175 2.29289322,14.2928932 L8.29289322,8.29289322 C8.67147216,7.91431428 9.28105859,7.90106866 9.67572463,8.26284586 L15.6757246,13.7628459 C16.0828436,14.1360383 16.1103465,14.7686056 15.7371541,15.1757246 C15.3639617,15.5828436 14.7313944,15.6103465 14.3242754,15.2371541 L9.03007575,10.3841378 L3.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" transform="translate(9.000003, 11.999999) rotate(-270.000000) translate(-9.000003, -11.999999) "></path>
          </g>
      </svg>
  ';
      break;
    case 'calendar_list':
      $icon = '<i class="fa-solid fa-calendar-week ' . $class . ' "></i>';
      break;
    case 'giderler':
      $icon = '<i class="fas fa-wallet ' . $class . ' "></i>';
      break;
    case 'odemeler':
      $icon = '<i class="fas fa-money-check ' . $class . ' "></i>';
      break;
    case 'faturalar':
      $icon = '<i class=" fa-solid fa-receipt ' . $class . ' "></i>';
      break;
    case 'ecari':
      $icon = '<i class="fa-solid fa-file-invoice ' . $class . ' "></i>';
      break;
    case 'roles':
      $icon = '<i class="fas fa-user-tag ' . $class . ' "></i>';
      break;
    case 'save':
      $icon = '<i class="fas fa-save ' . $class . ' "></i>';
      break;
    case 'giderler_tanim':
      $icon = '<i class="fas fa-tags ' . $class . ' "></i>';
      break;
    case 'vehicles':
      $icon = '<i class="fas fa-car-side ' . $class . ' "></i>';
      break;
    case 'company_account':
      $icon = '<i class="fas fa-hand-holding-usd ' . $class . ' "></i>';
      break;
    case 'my_companies':
      $icon = '<i class="fas fa-building ' . $class . ' "></i>';
      break;
    case 'cari':
      $icon = '<i class="fas fa-money-check-alt ' . $class . ' "></i>';
      break;
    case 'link':
      $icon = '<i class="fas fa-external-link-alt ' . $class . ' "></i>';
      break;
    case 'captain':
      $icon = '<i class="fas fa-file-signature ' . $class . ' "></i>';
      break;
    case 'pinned':
      $icon = '<i class="fas fa-thumbtack" ' . $class . '></i>';
      break;
    case 'offer':

      $icon = '<i class="fas fa-file-invoice-dollar ' . $class . '"></i>';
      break;
    case 'light':
      $icon = '<i class="fas fa-lightbulb ' . $class . '"></i>';
      break;
    case 'document':
      $icon = '<i class="fas fa-file-alt ' . $class . '"></i>';
      break;
    case 'upload':
      $icon = '<i class="fas fa-upload ' . $class . '"></i>';
      break;
    case 'hotels':
      $icon = '<i class="fas fa-hotel ' . $class . '"></i>';
      break;
    case 'view':
      $icon = '<i class="fa fa-search ' . $class . '"></i>';
      break;
    case 'trans':
      $icon = '<i class="fas fa-store ' . $class . '"></i>';
      break;
    case 'ouritems':
      $icon = '<i class="fa fa-fill-drip ' . $class . '"></i>';
      break;
    case 'define':
      $icon = '<i class="fas fa-certificate ' . $class . '"></i>';
      break;
    case 'print':
      $icon = '<i class="fas fa-print ' . $class . '"></i>';
      break;
    case 'edit':
      $icon = '<i class="fas fa-edit ' . $class . '"></i>';
      break;
    case 'plus':
      $icon = '<i class="fas fa-plus ' . $class . '"></i>';
      break;
    case 'event':
    case 'calendar':
      $icon = '<i class="fas fa-calendar-alt ' . $class . '"></i>';
      break;
    case 'projects':
      $icon = '<i class="fas fa-project-diagram ' . $class . '"></i>';
      break;
    case 'ships':
      $icon = '<i class="fas fa-ship ' . $class . '"></i>';
      break;
    case 'customers':
      $icon = '<i class="fa fa-users ' . $class . '" aria-hidden="true"></i>';
      break;
    case 'agency':
      $icon = '<i class="fas fa-building ' . $class . '"></i>';
      break;
    case 'supplyers':
      $icon = '<i class="fas fa-industry ' . $class . '"></i>';
      break;
    case 'serviceproviders':
      $icon = '<i class="fa fa-wrench ' . $class . '" aria-hidden="true"></i>';
      break;
    case 'employee':
      $icon = '<i class="fa fa-id-card ' . $class . '" aria-hidden="true"></i>';
      break;
    case 'petition':
      $icon = '<i class="fas fa-file ' . $class . '"></i>';
      break;
    case 'dashboard':
      $icon = '<i class="fas fa-columns ' . $class . '"></i>';
      break;
    case 'allowance':
    case 'avans_talebi':
      $icon = '<i class="fas fa-coins ' . $class . '"></i>';
      break;
    case 'avans_kapama':
      $icon = '<i class="fas fa-store-slash ' . $class . '"></i>';
      break;
    case 'todo':
    case 'todos':
      $icon = '<i class="fas fa-tasks ' . $class . '"></i>';
      break;
    case 'mail':
      $icon = '<i class="fas fa-envelope-open-text ' . $class . '"></i>';
      break;
    case 'notification':
      if ($target == 'Business') $class = 'text-success icon-lg';
      if ($target == 'Personal') $class = 'text-warning icon-lg';
      if ($target == 'Family') $class = 'text-primary icon-lg';
      if ($target == 'Project') $class = 'text-danger icon-lg';

      $icon = '<i class="fas fa-bell ' . $class . '"></i>';
      break;
    case 'invoices':
      $icon = '<i class="fas fa-file-invoice ' . $class . '"></i>';
      break;
    case 'tag':
      $icon = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                    <g stroke="none" stroke-width="1" fill="none">
                                                        <polygon points=" 0 0 24 0 24 24 0 24"></polygon>
                                                        <path d="M3.52270623,14.028695 C2.82576459,13.3275941 2.82576459,12.19529 3.52270623,11.4941891 L11.6127629,3.54050571 C11.9489429,3.20999263 12.401513,3.0247814 12.8729533,3.0247814 L19.3274172,3.0247814 C20.3201611,3.0247814 21.124939,3.82955935 21.124939,4.82230326 L21.124939,11.2583059 C21.124939,11.7406659 20.9310733,12.2027862 20.5869271,12.5407722 L12.5103155,20.4728108 C12.1731575,20.8103442 11.7156477,21 11.2385688,21 C10.7614899,21 10.3039801,20.8103442 9.9668221,20.4728108 L3.52270623,14.028695 Z M16.9307214,9.01652093 C17.9234653,9.01652093 18.7282432,8.21174298 18.7282432,7.21899907 C18.7282432,6.22625516 17.9234653,5.42147721 16.9307214,5.42147721 C15.9379775,5.42147721 15.1331995,6.22625516 15.1331995,7.21899907 C15.1331995,8.21174298 15.9379775,9.01652093 16.9307214,9.01652093 Z" fill="#000000" fill-rule="nonzero" opacity="1"></path>
                                                    </g>
                                                </svg>';
      break;
    case 'Business':
      $icon = '<!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-02-01-052524/theme/html/demo6/dist/../src/media/svg/icons/Communication/Group.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <polygon points="0 0 24 0 24 24 0 24"/>
        <path d="M18,14 C16.3431458,14 15,12.6568542 15,11 C15,9.34314575 16.3431458,8 18,8 C19.6568542,8 21,9.34314575 21,11 C21,12.6568542 19.6568542,14 18,14 Z M9,11 C6.790861,11 5,9.209139 5,7 C5,4.790861 6.790861,3 9,3 C11.209139,3 13,4.790861 13,7 C13,9.209139 11.209139,11 9,11 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
        <path d="M17.6011961,15.0006174 C21.0077043,15.0378534 23.7891749,16.7601418 23.9984937,20.4 C24.0069246,20.5466056 23.9984937,21 23.4559499,21 L19.6,21 C19.6,18.7490654 18.8562935,16.6718327 17.6011961,15.0006174 Z M0.00065168429,20.1992055 C0.388258525,15.4265159 4.26191235,13 8.98334134,13 C13.7712164,13 17.7048837,15.2931929 17.9979143,20.2 C18.0095879,20.3954741 17.9979143,21 17.2466999,21 C13.541124,21 8.03472472,21 0.727502227,21 C0.476712155,21 -0.0204617505,20.45918 0.00065168429,20.1992055 Z" fill="#000000" fill-rule="nonzero"/>
    </g>
</svg><!--end::Svg Icon-->';
      break;
    case 'Personal':
      $icon = '<!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-02-01-052524/theme/html/demo6/dist/../src/media/svg/icons/Home/Chair1.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M12,2 C13.8385982,2 15.5193947,3.03878936 16.3416408,4.68328157 L19,10 C20.365323,12.730646 19.25851,16.0510849 16.527864,17.4164079 C15.7602901,17.8001948 14.9139019,18 14.0557281,18 L9.94427191,18 C6.8913169,18 4.41640786,15.525091 4.41640786,12.472136 C4.41640786,11.6139622 4.61621302,10.767574 5,10 L7.65835921,4.68328157 C8.48060532,3.03878936 10.1614018,2 12,2 Z M7.55,13.6 C9.00633458,14.6922509 10.4936654,15.25 12,15.25 C13.5063346,15.25 14.9936654,14.6922509 16.45,13.6 L15.55,12.4 C14.3396679,13.3077491 13.1603321,13.75 12,13.75 C10.8396679,13.75 9.66033208,13.3077491 8.45,12.4 L7.55,13.6 Z" fill="#000000"/>
        <path d="M6.15999985,21.0604779 L8.15999985,17.5963763 C8.43614222,17.1180837 9.04773263,16.9542085 9.52602525,17.2303509 C10.0043179,17.5064933 10.168193,18.1180837 9.89205065,18.5963763 L7.89205065,22.0604779 C7.61590828,22.5387706 7.00431787,22.7026457 6.52602525,22.4265033 C6.04773263,22.150361 5.88385747,21.5387706 6.15999985,21.0604779 Z M17.8320512,21.0301278 C18.1081936,21.5084204 17.9443184,22.1200108 17.4660258,22.3961532 C16.9877332,22.6722956 16.3761428,22.5084204 16.1000004,22.0301278 L14.1000004,18.5660262 C13.823858,18.0877335 13.9877332,17.4761431 14.4660258,17.2000008 C14.9443184,16.9238584 15.5559088,17.0877335 15.8320512,17.5660262 L17.8320512,21.0301278 Z" fill="#000000" opacity="0.3"/>
    </g>
</svg><!--end::Svg Icon-->';
      break;
    case 'Family':
      $icon = '<!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-02-01-052524/theme/html/demo6/dist/../src/media/svg/icons/Home/Bed.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M4,22 L2,22 C2,19.2385763 4.23857625,18 7,18 L17,18 C19.7614237,18 22,19.2385763 22,22 L20,22 C20,20.3431458 18.6568542,20 17,20 L7,20 C5.34314575,20 4,20.3431458 4,22 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
        <rect fill="#000000" x="1" y="14" width="22" height="6" rx="1"/>
        <path d="M13,13 L11,13 L11,12 C11,11.4477153 10.5522847,11 10,11 L6,11 C5.44771525,11 5,11.4477153 5,12 L5,13 L4,13 C3.44771525,13 3,12.5522847 3,12 L3,8 C3,6.8954305 3.8954305,6 5,6 L19,6 C20.1045695,6 21,6.8954305 21,8 L21,12 C21,12.5522847 20.5522847,13 20,13 L19,13 L19,12 C19,11.4477153 18.5522847,11 18,11 L14,11 C13.4477153,11 13,11.4477153 13,12 L13,13 Z" fill="#000000" opacity="0.3"/>
    </g>
</svg><!--end::Svg Icon-->';
      break;
    case 'clone':
      $icon = '<i class="fa-solid fa-copy ' . $class . '"></i>';
      break;
    case 'Project':
      $icon = '<!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-02-01-052524/theme/html/demo6/dist/../src/media/svg/icons/Home/Building.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M13.5,21 L13.5,18 C13.5,17.4477153 13.0522847,17 12.5,17 L11.5,17 C10.9477153,17 10.5,17.4477153 10.5,18 L10.5,21 L5,21 L5,4 C5,2.8954305 5.8954305,2 7,2 L17,2 C18.1045695,2 19,2.8954305 19,4 L19,21 L13.5,21 Z M9,4 C8.44771525,4 8,4.44771525 8,5 L8,6 C8,6.55228475 8.44771525,7 9,7 L10,7 C10.5522847,7 11,6.55228475 11,6 L11,5 C11,4.44771525 10.5522847,4 10,4 L9,4 Z M14,4 C13.4477153,4 13,4.44771525 13,5 L13,6 C13,6.55228475 13.4477153,7 14,7 L15,7 C15.5522847,7 16,6.55228475 16,6 L16,5 C16,4.44771525 15.5522847,4 15,4 L14,4 Z M9,8 C8.44771525,8 8,8.44771525 8,9 L8,10 C8,10.5522847 8.44771525,11 9,11 L10,11 C10.5522847,11 11,10.5522847 11,10 L11,9 C11,8.44771525 10.5522847,8 10,8 L9,8 Z M9,12 C8.44771525,12 8,12.4477153 8,13 L8,14 C8,14.5522847 8.44771525,15 9,15 L10,15 C10.5522847,15 11,14.5522847 11,14 L11,13 C11,12.4477153 10.5522847,12 10,12 L9,12 Z M14,12 C13.4477153,12 13,12.4477153 13,13 L13,14 C13,14.5522847 13.4477153,15 14,15 L15,15 C15.5522847,15 16,14.5522847 16,14 L16,13 C16,12.4477153 15.5522847,12 15,12 L14,12 Z" fill="#000000"/>
        <rect fill="#FFFFFF" x="13" y="8" width="3" height="3" rx="1"/>
        <path d="M4,21 L20,21 C20.5522847,21 21,21.4477153 21,22 L21,22.4 C21,22.7313708 20.7313708,23 20.4,23 L3.6,23 C3.26862915,23 3,22.7313708 3,22.4 L3,22 C3,21.4477153 3.44771525,21 4,21 Z" fill="#000000" opacity="0.3"/>
    </g>
</svg><!--end::Svg Icon-->';
      break;
    case 'trash':
      $icon = '<!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-02-01-052524/theme/html/demo6/dist/../src/media/svg/icons/Home/Trash.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M6,8 L18,8 L17.106535,19.6150447 C17.04642,20.3965405 16.3947578,21 15.6109533,21 L8.38904671,21 C7.60524225,21 6.95358004,20.3965405 6.89346498,19.6150447 L6,8 Z M8,10 L8.45438229,14.0894406 L15.5517885,14.0339036 L16,10 L8,10 Z" fill="#000000" fill-rule="nonzero"/>
        <path d="M14,4.5 L14,3.5 C14,3.22385763 13.7761424,3 13.5,3 L10.5,3 C10.2238576,3 10,3.22385763 10,3.5 L10,4.5 L5.5,4.5 C5.22385763,4.5 5,4.72385763 5,5 L5,5.5 C5,5.77614237 5.22385763,6 5.5,6 L18.5,6 C18.7761424,6 19,5.77614237 19,5.5 L19,5 C19,4.72385763 18.7761424,4.5 18.5,4.5 L14,4.5 Z" fill="#000000" opacity="0.3"/>
    </g>
</svg><!--end::Svg Icon-->';
      break;
    case 'profile':
      $icon = '<i class="fas fa-user-alt ' . $class . '"></i>';
      break;
    case 'reminder':
      $icon = '<i class="fas fa-clock ' . $class . '"></i>';
      break;
    case 'delete':
      $icon = '<i class="fas fa-trash-alt ' . $class . '"></i>';
      break;
    case 'crop':
      $icon = '<i class="fas fa-crop ' . $class . '"></i>';
      break;
  }

  if ($return)
    return $icon;
  else
    echo $icon;
}
function icon_get($target, $class = '')
{

  return icon($target, $class, 1);
}
function table_user_list_avatar($data, $image_url = "")
{
  global $target;

  $edit_link = fn_menu_link($target . '/update&id=' . $data->id, 1);

  if (empty($image_url)) {
    $img_src = images_url($target, 'url');
  } else {
    $img_src = $image_url;
  }

  if ($data->avatar) {
    $img_src = T . $img_src . $data->avatar . '&h=80';
    $img_src = '<img class="img-fluid" src="' . $img_src . '" alt="photo">';
  } else {
    $img_src = '<span class="symbol-label  bg-light-primary text-primary fs-6 fw-bolder ">' . $data->name[0] . '</span>';
  }

  $firma_adi = ($data->firma_adi) ? $data->firma_adi : se('firma_adi');
  $html = <<<"EOT"
  <div class="d-flex align-items-center">
      <div class="symbol  symbol-40px symbol-circle ">$img_src</div>
      <div class="ms-4">
          <a href="$edit_link" class="fs-6 fw-bold text-gray-900 text-hover-primary mb-2">$data->name $data->lastname</a>
          <div class="fw-semibold fs-7 text-muted">$firma_adi</div>
      </div>
  </div>
      
EOT;
  echo $html;
}

function table_list_avatar($data, $image_url = "")
{
  global $target, ${$target . "_web_images"}, $PERMISSION_TARGET;

  if ($PERMISSION_TARGET['update'])
    $edit_link = fn_menu_link($target . '/update&id=' . $data->id, 1);
  else
    $edit_link = '#';

  if (empty($image_url)) {
    $img_src = ${$target . "_web_images"};
  } else {
    $img_src = $image_url;
  }

  if (!empty($data->avatar)) {
    $img_src = $img_src . $data->avatar;
    $img_src = '<div class="symbol symbol-40px symbol-circle symbol-sm"><img class="" src="' . $img_src . '" alt="photo"></div>';
  } else {
    $img_src = '<div class="symbol symbol-40px symbol-circle symbol-light-success"><span class="symbol-label font-size-h4">' . $data->adi[0] . '</span></div>';
  }

  if (is_object($data) && $data->telefon) {
    $telefon = '<a href="tel:' . $data->telefon . '" class="text-muted fw-bold">' . $data->telefon . '</a>';
  }
  $html = <<<"EOT"
      <span style="width: 250px;">
      <div class="d-flex align-items-center">
          <a href="$edit_link">$img_src</a>
          <div class="ms-3 ">                                  
              <a href="$edit_link" class="fw-bold text-dark text-hover-info">$data->firma_adi</a>
              <a href="$edit_link" class="text-muted d-block">$data->adi $data->soyadi</a>
              $telefon
          </div>                          
      </div>
  </span>
EOT;
  echo $html;
}
function table_customer_list_avatar($data, $image_url = "")
{
  global $target, ${$target . "_web_images"};

  $edit_link = fn_menu_link($target . '/update&id=' . $data->id, 1);

  if (empty($image_url)) {
    $img_src = ${$target . "_web_images"};
  } else {
    $img_src = $image_url;
  }

  if (!empty($data->logo)) {
    $img_src = T . $img_src . $data->logo . '&w=80&z=1';
    $img_src = '<div class="symbol symbol-40px"><img class="img-fluid" src="' . $img_src . '" alt="photo"></div>';
  } else {
    $img_src = '<div class="symbol symbol-40px  symbol-light-success"><span class="symbol-label font-size-h4">' . $data->firma_adi[0] . '</span></div>';
  }

  if (is_object($data) && $data->telefon) {
    $telefon = '<a href="tel:' . $data->telefon . '" class="text-muted">' . $data->telefon . '</a>';
  }
  $html = <<<"EOT"
      <span style="width: 250px;">
      <div class="d-flex align-items-center">
          <a href="$edit_link">$img_src</a>
          <div class="ms-3">                                  
              <a  class="text-dark text-hover-info fw-bold" href="$edit_link">$data->firma_adi</a>
              <a href="$edit_link" class="text-muted fw-bold">$data->adi $data->soyadi</a>
              $telefon
          </div>                          
      </div>
  </span>
EOT;
  echo $html;
}

function menu_item_here($mtarget)
{
  global $target;

  $targets = explode(',', $mtarget);
  if (!is_array($targets)) {
    $targets = array();
    $targets[] = $mtarget;
  }

  foreach ($targets as $k) {
    if ($target == $k) {
      echo 'here show';
      break;
    }
  }
}
function menu_item_active($__target)
{
  global $target, $mode;

  list($_target, $_mode) = explode('/', $__target);

  if ($_mode) {
    if ($__target == $target . '/' . $mode) {
      echo 'active';
    }
  } else {
    if ($target == $_target) {
      echo 'active';
    }
  }
}
//
/// Make Seo
//
function fn_MakeSeo($text)
{
  // $text = str_replace('I', 'i', $text);

  // $text = preg_replace('/[^a-zA-Z0-9]/i', '', $text);
  $text = @str_replace(" ", "-", trim($text));
  $text = @str_replace('\'', "-", trim($text));
  $text = @str_replace('.', "-", trim($text));

  $text = @str_replace("â", "a", trim($text));
  $text = @str_replace("Â", "A", trim($text));

  $text = @preg_replace("@[^A-Za-z0-9\-_ĞÜŞİÖÇğüşıöç]+@i", "", $text);
  $text = @str_replace(" +", " ", trim($text));
  $text = @str_replace("[-]+", "-", $text);
  $text = @str_replace("[_]+", "_", $text);
  $text = strtolowerTR($text);
  if ((substr($text, -1) == '_') || (substr($text, -1) == '-')) $text = substr($text, 0, -1);

  return $text;
}

//
/// Make Seo for Murettebat
//

// @worqzone
// 20 Agustos 2021 12.40
// AcikHava Ziynet Oncesi ve Didim
// Cnmar icin
function fn_MakeSeoArray($text)
{

  $text = @str_replace(" ", "", trim($text));
  $text = @str_replace('\'', "_", trim($text));
  $text = @str_replace('.', "_", trim($text));
  $text = @str_replace('-', "_", trim($text));

  $text = @str_replace("â", "a", trim($text));
  $text = @str_replace("Â", "A", trim($text));

  $text = @preg_replace("@[^A-Za-z0-9\-_ĞÜŞİÖÇğüşıöç]+@i", "", $text);
  $text = @str_replace(" +", " ", trim($text));
  $text = @str_replace("[-]+", "_", $text);
  $text = @str_replace("[_]+", "_", $text);
  $text = strtolowerTR($text);
  if ((substr($text, -1) == '_') || (substr($text, -1) == '-')) $text = substr($text, 0, -1);
  return $text;
}
function strtolowerTR($text)
{
  $TRBul = array('Â', 'Ğ', 'Ü', 'Ş', 'İ', 'Ö', 'Ç', 'ğ', 'ü', 'ş', 'ı', 'ö', 'ç');
  $TRDegistir = array('a', 'g', 'u', 's', 'i', 'o', 'c', 'g', 'u', 's', 'i', 'o', 'c');
  $text = str_replace($TRBul, $TRDegistir, $text);
  $text = strtolower($text);
  return $text;
}
