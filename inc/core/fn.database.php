<?php
/*
**************************************************
*****************EVRİM EZ SQL EKLENTISI***********
**************************************************
*/

//
// Tablodak<PERSON> s<PERSON>tünları getir.
//
function fn_get_table_fields($table_name, $exclude = array())
{
	global $db;

	$rows = $db->get_results("SHOW COLUMNS FROM $table_name");
	if (is_array($rows)) {
		$fields = array();
		foreach ($rows as $k => $v) {
			if (!in_array($v->Field, $exclude)) {
				$fields[] = $v->Field;
			}
		}
		return $fields;
	}
	return false;
}

//
// Check keys from array to correspond table fields
// $data array format:
// 'field_name1' => 'value1',
// 'field_name2' => 'value2'
function fn_check_table_fields($data, $table_name)
{


	$_fields = fn_get_table_fields($table_name);
	// if (@in_array('lang', $_fields)) {

	// 	$data['lang'] = $_SESSION['lang'];
	// }

	if (is_array($_fields)) {
		foreach ($data as $k => $v) {
			if (!in_array($k, $_fields)) {
				unset($data[$k]);
			}
		}
		if (func_num_args() > 2) {
			for ($i = 2; $i < func_num_args(); $i++) {
				unset($data[func_get_arg($i)]);
			}
		}
		return $data;
	}
	return false;
}

//
// Form insert query by array and executes it.
//
function fn_insert_array($table_name, $data, $replace = false)
{
	global $db;

	$data['c_date'] = time();
	$data['action_owner_id'] = $_SESSION[strtolower(AREA)]['user_login'];
	$data['isletme_id'] = ISLETME_ID;
	$data = fn_check_table_fields($data, $table_name);

	if (!empty($table_name) && !empty($data)) {
		$query = (($replace == true) ? 'replace' : 'insert') . " INTO $table_name (`" . implode('`, `', array_keys($data)) . "`) VALUES ('" . implode('\', \'', array_values($data)) . "');";

		//if ($db->insert_i==0) { }
		/*return ($db->query($query) == false) ? false : $db->insert_id;*/

		if ($db->query($query) == false) {
			echo $query;
			exit();
		} else return $db->insert_id;
	}

	return false;
}


//
// Form update query by array and executes it
//
function fn_update_array($table_name, $data, $where = '')
{
	global $db;

	if (isset($_SESSION['user_login']))
		$data['action_owner_id'] = $_SESSION['user_login'];

	$data['u_date'] = time();
	$data = fn_check_table_fields($data, $table_name);


	$q = '';
	if (!empty($table_name) && !empty($data)) {
		foreach ($data as $k => $v) {
			$q .= ($q ? ", " : "") . "`" . $k . "`='" . $v . "'";
		}
		// echo "UPDATE $table_name SET $q" . ($where ? " WHERE " . $where : "");
		return $db->query("UPDATE $table_name SET $q" . ($where ? " WHERE " . $where : ""));
	}
	return false;
}


//
/// COK YIL GECTI: Acaba hangi yil bu dosyasi olusturmustum! Kim bilir?
/// 2021 Subat 16: @worqzone
//
function fn_delete_row($table, $where)
{
	global $db;

	$db->query("DELETE FROM $table WHERE 1=1 AND $where");
}
