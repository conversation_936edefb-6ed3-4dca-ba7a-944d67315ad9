<?php
function se($col)
{

	return $_SESSION[strtolower(AREA)][$col];
}
function images_url($_target, $t = 'file', $isletme_id = 0)
{

	$isletme_id = ($isletme_id) ? $isletme_id : ISLETME_ID;

	if ($t == 'file')
		return  IMAGES_DIR_OUT . '/' . $_target . '/' . $isletme_id . DS;
	else
		return  IMAGES_WEB_DIR_OUT . '/' . $_target . '/' . $isletme_id . DS;
}

function aktivity_fw_belirle($c_target, $c_mode)
{
	switch ($c_target) {
		case 'orders':
			$color = 'bold';
			break;

		default:
			$color = 'normal';
			break;
	}
	return $color;
}
function aktivity_renk_belirle($c_target, $c_mode)
{
	switch ($c_target) {
		case 'orders':
			$color = 'success';
			break;
		case 'egitim':
			$color = 'warning';
			break;
		case 'sertifika':
			$color = 'danger';
			break;

		default:
			$color = 'primary';
			break;
	}
	return $color;
}
function add_activity($_data)
{
	$_ac = array();
	$_ac = $_data;
	if (empty($_ac['link']))
		$_ac['link'] = '#';
	if (empty($_ac['user_id']))
		$_ac['user_id'] = USER_LOGIN;
	$_ac['c_date'] = time();

	fn_insert_array('aktiviteler', $_ac);
}
// Ritim Online V2 icin php upgrade 8.2
// 23 Ekim 2024 11:22 Workinton
// Asi Hasta ben yorgun.
function is_say($ar)
{
	if (function_exists('is_countable'))
		return (is_countable($ar)) ? count($ar) : '0';
	else
		return count($ar);
}
function say($ar)
{
	return is_say($ar);
}


function yuzde_hesapla($fiyat, $yuzde)
{

	if ($yuzde < 10) $yuzde = "0$yuzde";
	$sayi = floatval("0" . '.' . $yuzde);
	return round($fiyat * $sayi);
}
function clean_for_sms($string)
{
	$clean_string =  preg_replace('/[^0-9]/', '', $string);

	if ($clean_string[0] == '0') {
		$ready_str = substr($clean_string, 1);
	} else {
		$ready_str = $clean_string;
	}
	return $ready_str;
}
function sendSMS($message, $phones)
{
	$sms_msg = array(
		"username" => "902243220645", // https://oim.verimor.com.tr/sms_settings/edit adresinden öğrenebilirsiniz.
		"password" => "Ritim2024!", // https://oim.verimor.com.tr/sms_settings/edit adresinden belirlemeniz gerekir.
		"source_addr" => "02243220645", // Gönderici başlığı, https://oim.verimor.com.tr/headers adresinde onaylanmış olmalı, değilse 400 hatası alırsınız.
		//    "valid_for" => "48:00",
		//    "send_at" => "2015-02-20 16:06:00",
		//    "datacoding" => "0",
		"custom_id" => "1424441160.9331344",
		"messages" => array(
			array(
				"msg" => $message,
				"dest" => $phones
			)
		)
	);
	$ch = curl_init('https://sms.verimor.com.tr/v2/send.json');
	curl_setopt_array($ch, array(
		CURLOPT_POST => TRUE,
		CURLOPT_RETURNTRANSFER => TRUE,
		CURLOPT_HTTPHEADER => array('Content-Type: application/json'),
		CURLOPT_POSTFIELDS => json_encode($sms_msg),
	));
	$http_response = curl_exec($ch);
	$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	if ($http_code != 200) {
		echo "$http_code $http_response\n";
		return false;
	}

	return $http_response;
}
// Zaman! 26 Aralik 2022
function is_developer($action)
{
	return true;
	switch ($action) {
		case 'v25':
			if ($_SESSION['a']['user_id'] == '18') {
				return true;
			}
			break;
		case 'video_upload':
			return true;
			if (AREA == 'A') {
				if (USER_LOGIN == '18') return true;
			}
			if (USER_LOGIN == '165') return true;
			break;
		case 'anket':
			return true;
			if (AREA == 'A') {
				if (USER_LOGIN == '18') return true;
			}
			if (USER_LOGIN == '165') return true;
			break;
	}
	return false;
}
// para para para
function para($para)
{
	echo $para;
}
// 16 Haziran 2021 13:26
// Worqzone, yeni klavye ile yaziyorum.
// Yalniz gecmis bi gecenin sabahina
function yoneticilere_bildirim($notifi)
{

	$yoneticiler = get_results('users', "user_type='Admin' AND is_delete=0", "user_id");
	foreach ($yoneticiler as $k => $v) {

		$notifi['owner_id'] = $v->user_id;
		fn_create_notification($notifi);
	}


	fn_set_notice(_('Yönetici onayına gönderildi'), 'W');
}

// Function to get the client IP address
function get_client_ip()
{
	$ipaddress = '';
	if (getenv('HTTP_CLIENT_IP'))
		$ipaddress = getenv('HTTP_CLIENT_IP');
	else if (getenv('HTTP_X_FORWARDED_FOR'))
		$ipaddress = getenv('HTTP_X_FORWARDED_FOR');
	else if (getenv('HTTP_X_FORWARDED'))
		$ipaddress = getenv('HTTP_X_FORWARDED');
	else if (getenv('HTTP_FORWARDED_FOR'))
		$ipaddress = getenv('HTTP_FORWARDED_FOR');
	else if (getenv('HTTP_FORWARDED'))
		$ipaddress = getenv('HTTP_FORWARDED');
	else if (getenv('REMOTE_ADDR'))
		$ipaddress = getenv('REMOTE_ADDR');
	else
		$ipaddress = 'UNKNOWN';
	return $ipaddress;
}

function set_postion($table, $position = 0)
{

	if ($position) {
		$last_id =  get_var($table, "position", "1=1 order by id desc");
		return $last_id + 10;
	} else {
		$last_id =  get_var($table, "id", "1=1 order by id desc");
		return $last_id + 1;
	}
}


function fn_create_notification($data, $send_notification = 0)
{
	$id = fn_insert_array('notifications', $data);
}

//Code written by purpledesign.in Jan 2014
function dateDiff($date)
{
	$mydate = time();
	$theDiff = "";
	//echo $mydate;//2014-06-06 21:35:55
	$datetime1 = date_create("@$date");
	$datetime2 = date_create("@$mydate");
	$interval = date_diff($datetime1, $datetime2);
	//echo $interval->format('%s Seconds %i Minutes %h Hours %d days %m Months %y Year    Ago')."<br>";

	if ($interval->invert == 1) {
		$ek = _('sonra');
		$gecmis = 0;
	} else {
		$ek = _('önceydi');
		$gecmis = 1;
	}
	$min = $interval->format('%i');
	$sec = $interval->format('%s');
	$hour = $interval->format('%h');
	$mon = $interval->format('%m');
	$day = $interval->format('%d');
	$year = $interval->format('%y');
	if ($interval->format('%i%h%d%m%y') == "00000") {
		$str = $sec . " " . _('Saniye') . ' ' . $ek;
	} else if ($interval->format('%h%d%m%y') == "0000") {
		$str = $min . " " . _('Dakika') . ' ' . $ek;
	} else if ($interval->format('%d%m%y') == "000") {
		$str = $hour . " " . _('Saat') . ' ' . $ek;
	} else if ($interval->format('%m%y') == "00") {
		$str = $day . " " . _('Gün') . ' ' . $ek;
	} else if ($interval->format('%y') == "0") {
		$str = $mon . " " . _('Hafta') . ' ' . $ek;
	} else {
		$str = $year . " " . _('Yıl') . ' ' . $ek;
	}

	return array($str, $gecmis);
}
//
/// CNMAR icin  yapildi
/// Ornek Format: 27/01/2021 - Wedsnday
//
function eta_format($data, $time = '', $period = '')
{
	if (empty($data)) return 'TBA';
	$tarih =  date('d/m/Y', $data);
	return trim($tarih . ' ' . $time . ' ' . $period . ' - ' . date('l', $data));
}
function send_json($data)
{
	if (!headers_sent()) header('Content-Type: application/json; charset=utf-8', true, 200);
	echo json_encode($data);
	exit();
}

function get_image_path($module)
{
	global  ${$module . "_images_dir"}, ${$module . "_web_images_dir"};

	return array(${$module . "_images_dir"}, ${$module . "_web_images_dir"});
}
// Tablodaki Enum tiplerini getirir
function get_enum_values($table, $field)
{
	global $db;
	$sonuc = $db->get_row("SHOW COLUMNS FROM {$table} WHERE Field = '{$field}'");
	$type = $sonuc->Type;
	preg_match("/^enum\(\'(.*)\'\)$/", $type, $matches);
	$enum = explode("','", $matches[1]);
	return $enum;
}

function hex2rgb($hex)
{
	$hex = str_replace("#", "", $hex);

	if (strlen($hex) == 3) {
		$r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
		$g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
		$b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
	} else {
		$r = hexdec(substr($hex, 0, 2));
		$g = hexdec(substr($hex, 2, 2));
		$b = hexdec(substr($hex, 4, 2));
	}
	$rgb = 'rgb(' . $r . ', ' . $g . ', ' . $b . ')';
	//return implode(",", $rgb); // returns the rgb values separated by commas
	return $rgb; // returns an array with the rgb values
}


// 27 Kasim 2019
// Prof Ajans
// 12.50
// Besiktas projesi
//
/// file upload array
//
function fn_file_upload_array($userfile_name, $userfile_tmp, $file_upload_dir, $koruma = false, $allowed_video_types = array())
{

	if (!$userfile_name) return false;

	if (empty($allowed_video_types))	$allowed_video_types = array('zip', 'flv', 'xml', 'rar', 'gif', 'jpg', 'png');

	//Get the file information
	// $userfile_name = $file['name'];
	// $userfile_tmp = $file['tmp_name'];
	// $userfile_size = $file['size'];
	// $userfile_type = $file['type'];
	$filename = basename($userfile_name);
	$file_ext = strtolower(substr($filename, strrpos($filename, '.') + 1));


	// upload image name declare
	$x_t = end(explode(".", $userfile_name));

	$x_t = "." . $x_t;

	@$upload_image_name = str_replace($x_t, "", $userfile_name);


	// koruma
	if ($koruma) :
		if (!in_array($file_ext, $allowed_video_types)) {

			fn_set_notice("O", "<strong>$userfile_name</strong> isimli dosya yüklenemedi.");
			return false;
		}
	endif;

	$large_image_name = fn_MakeSeo($upload_image_name) . "." . $file_ext;

	$large_image_location = $file_upload_dir . $large_image_name;

	if (move_uploaded_file($userfile_tmp, $large_image_location)) {

		return 	$large_image_name;
	}

	return false;
}

//
/// Page page
//
function pagepage_frond($toplam_sonuc, $type = '')
{
	global $pagepage_max;

	// PageControl(total number of entries, entries show in each page, current page)
	$pagecon = new PageControl($toplam_sonuc, $pagepage_max, $_GET['page']);

	$filter = $_GET['filter'];
	if (AREA == 'C') {
		// $temp = explode("/page/",$_SERVER['REQUEST_URI']);
		// $seo_link_b = $temp[0].'/page/';
		$str = '&';
		if (!$filter) {
			$str = '?';
		}
		$temp = explode($str . "page=", $_SERVER['REQUEST_URI']);
		$seo_link_b = $temp[0] . $str . 'page=';
	} else {
		$temp = explode("&page=", $_SERVER['REQUEST_URI']);
		$seo_link_b = $temp[0] . '&page=';
	}

	// evrim 20 ekim 2015 aksam 21.51 Ask yeniden izlerken. Burcu instagram da yine. # Ozgurluk Projesi
	// evrim 20 ekim 2016 oglen 12.18 Abi ofis. Meto ve ben. Yine ayni meto! Kodlamaya devam.

	if ($type == 'arama') {

		$filter = $_GET['filter'];
		$str = '&';
		if (!$filter) {
			$str = '?';
		}
		$temp = explode($str . "page=", $_SERVER['REQUEST_URI']);
		$seo_link_b = $temp[0] . $str . 'page=';
	}



	if ($pagecon->pages > 1) :

		// echo '<ul class="pagination pagination-v2">';

		echo $pagecon->getList(array(
			'link'	=>	"<a class='page-numbers' href='" . $seo_link_b . "#PAGE#'>#PAGE#</a>",	//the link of each page, #PAGE# is the page number
			'current'	=>	"<span class='page-numbers current'>#PAGE#</span>",	//how the current page is showed
			'neighbor'	=>	8,	//how many pages next to the current page will be shown
			'headfoot'	=>	false,	//show the first and the last page number
			'skipped'	=>	"... ",	//what replaces the skipped pages replace
			'previous'	=>	"<a class='prev page-numbers' href='" . $seo_link_b . "#PAGE#'>&#8592;</a>",	//show the "previous" button, #PAGE# is the page number
			'next'	=>	"<a class='next page-numbers' href='" . $seo_link_b . "#PAGE#'>&rarr;</a>"	//show the "next" button, #PAGE# is the page number
		));

		// echo '</ul>';

		return $pagecon;
	endif;
}

function zamantr($girdi, $gun = false)
{
	if ($gun) {
		$cikti = date("d l", $girdi);
	} else
		$cikti = date("d F Y l H:i", $girdi);

	$aylarIng = array(
		"January",
		"February",
		"March",
		"April",
		"May",
		"June",
		"July",
		"August",
		"September",
		"October",
		"November",
		"December"
	);
	$gunlerIng = array("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");
	$aylar = array(
		"Ocak",
		"Şubat",
		"Mart",
		"Nisan",
		"Mayıs",
		"Haziran",
		"Temmuz",
		"Ağustos",
		"Eylül",
		"Ekim",
		"Kasım",
		"Aralık"
	);
	$gunler = array("Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi", "Pazar");
	$cikti = str_replace($aylarIng, $aylar, $cikti);
	$cikti = str_replace($gunlerIng, $gunler, $cikti);

	if ($gun) {
		return str_replace($gunlerIng, $gunler, $cikti);
	}
	return $cikti;
}

function get_results($table, $where = "1=1", $field = "", $return_array = "OBJECT")
{
	global $db;


	$field = ($field) ? $field : ' * ';
	// echo "SELECT $field FROM $table WHERE $where";
	try {
		return  $db->get_results("SELECT $field FROM $table WHERE $where", $return_array);
	} catch (Throwable $th) {
		// echo '<pre>';
		// echo "SELECT $field FROM $table WHERE $where";
		// echo '</pre>';
	}
}


function get_row($table, $where, $field = "", $return_array = "OBJECT")
{
	global $db;
	$field = ($field) ? $field : ' * ';
	// echo "SELECT $field FROM  $table WHERE $where limit 1" . '<br>';
	return  $db->get_row("SELECT $field FROM  $table WHERE $where limit 1", $return_array);
}

function get_var($table, $field, $where)
{
	global $db;
	// echo "SELECT $field FROM  $table WHERE $where limit 1";
	return  $db->get_var("SELECT $field FROM  $table WHERE $where limit 1");
}
function breadcrumbs($target = '')
{
?>
	<ul class="breadcrumb-v5">
		<li><a href="index.html"><i class="fa fa-home"></i></a></li>
		<li><a href="#">Products</a></li>
		<li class="active">New</li>
	</ul>
<?php
}

function the_discount_price($v)
{
?>


	<?php echo $v->fiyat + (($v->fiyat * $v->indirim)  / 100); ?> <?php echo $v->para_birimi ?>

	<?php
}

function the_price($v)
{
	$para_birimi = 'TL';
	if (isset($v->fiyat)) :

	?>

		<span class="font-weight-bolder fw-bold text-success"><?php echo $v->fiyat; ?> <?php echo $para_birimi ?></span>

<?php

	else :
		echo number_format($v, 2, ',', '.') . ' TL';

	endif;
}

function dd()
{
	array_map(function ($x) {
		var_dump($x);
		echo "<br>";
	}, func_get_args());
	die;
}



function is_ubcat($parent_id)
{
	global $db;

	return  $db->get_var("SELECT parent_id FROM  category WHERE category_id='$parent_id'  and status ='A' limit 1");
}
//
/// Page page
//
function pagepage($toplam_sonuc, $type = '')
{
	global $pagepage_max;
	include(LIB_DIR . "/pagepage/pagecontrol.class.php");
	// PageControl(total number of entries, entries show in each page, current page)
	$pagecon = new PageControl($toplam_sonuc, $pagepage_max, @$_GET['page']);


	if (AREA == 'C') {
		// $temp = explode("/page/",$_SERVER['REQUEST_URI']);
		// $seo_link_b = $temp[0].'/page/';
		$str = '&';
		if (!$filter) {
			$str = '?';
		}
		$temp = explode($str . "page=", $_SERVER['REQUEST_URI']);
		$seo_link_b = $temp[0] . $str . 'page=';
	} else {
		$temp = explode("&page=", $_SERVER['REQUEST_URI']);
		$seo_link_b = $temp[0] . '&page=';
	}

	// evrim 20 ekim 2015 aksam 21.51 Ask yeniden izlerken. Burcu instagram da yine. # Ozgurluk Projesi
	if ($type == 'arama') {

		$filter = $_GET['filter'];
		$str = '&';
		if (!$filter) {
			$str = '?';
		}
		$temp = explode($str . "page=", $_SERVER['REQUEST_URI']);
		$seo_link_b = $temp[0] . $str . 'page=';
	}



	if ($pagecon->pages > 1) :


		echo $pagecon->getList(array(
			'link'	=>	"<a class='btn btn-icon btn-sm border-0 btn-hover-primary mr-2 my-1' href='" . $seo_link_b . "#PAGE#'>#PAGE#</a> ",	//the link of each page, #PAGE# is the page number
			'current'	=>	"<a class='btn btn-icon btn-sm border-0 btn-hover-primary active mr-2 my-1' href='#'>#PAGE#</a>",	//how the current page is showed
			'neighbor'	=>	8,	//how many pages next to the current page will be shown
			'headfoot'	=>	false,	//show the first and the last page number
			'skipped'	=>	"... ",	//what replaces the skipped pages replace
			'previous'	=>	"<a class='btn btn-icon btn-sm btn-light-primary mr-2 my-1' href='" . $seo_link_b . "#PAGE#'><i class='ki ki-bold-arrow-back icon-xs'></i></a>",	//show the "previous" button, #PAGE# is the page number
			'next'	=>	"<a class='btn btn-icon btn-sm btn-light-primary mr-2 my-1' href='" . $seo_link_b . "#PAGE#'><i class='ki ki-bold-arrow-next icon-xs'></i></a>"	//show the "next" button, #PAGE# is the page number
		));


	endif;
}
//
/// Random string a integer
//
// function rasgele($uzunluk)
// {
// 	$karakterler = "1234567890";
// 	$key = '';
// 	for ($i = 0; $i < $uzunluk; $i++) {
// 		$key .= $karakterler{
// 			rand(0, 9)};
// 	}
// 	return $key;
// }
function rasgele($length)
{
	$number = '';
	for ($i = 0; $i < $length; $i++) {
		$number .= rand(0, 9);
	}

	return (int)$number;
	// $karakterler = "1234567890";
	// $key = '';
	// for ($i = 0; $i < $uzunluk; $i++) {
	// 	$key .= $karakterler{
	// 		rand(0, 9)};
	// }
	// return $key;
}

function fn_video_upload($file, $file_upload_dir, $spesifik_id)
{

	if (!$file) return false;

	if (!is_dir($file_upload_dir)) {
		if (!mkdir($file_upload_dir, 0777)) {
			die('Dizin oluşturulamadı....');
		}
	}

	//Get the file information
	$userfile_tmp = $file['tmp_name'];

	$uzantilar = explode('.', $file['name']);
	$uzanti = end($uzantilar);

	$large_image_name = $spesifik_id . '_' . time() . "." . $uzanti;

	$large_image_location = $file_upload_dir . $large_image_name;

	if (move_uploaded_file($userfile_tmp, $large_image_location)) {

		return 	$large_image_name;
	}

	return false;
}
//
/// file upload
//
function fn_file_upload($file, $file_upload_dir, $koruma = false, $allowed_video_types = array())
{

	if (!$file) return false;

	if (!is_dir($file_upload_dir)) {
		if (!mkdir($file_upload_dir, 0777)) {
			die('Dizin oluşturulamadı....: ' . $file_upload_dir);
		}
	}


	if (empty($allowed_video_types))	$allowed_video_types = array('pdf', 'jpeg',  'gif', 'jpg', 'png');

	//Get the file information
	$userfile_name = $file['name'];
	$userfile_tmp = $file['tmp_name'];
	$filename = strtolower(basename($file['name']));
	$file_ext = strtolower(substr($filename, strrpos($filename, '.') + 1));

	// upload image name declare
	$x_t = explode(".", $userfile_name);
	$x_t = end($x_t);

	$x_t = "." . $x_t;

	@$upload_image_name = str_replace($x_t, "", $userfile_name);


	// koruma
	if ($koruma) :
		if (!in_array($file_ext, $allowed_video_types)) {

			fn_set_notice("<strong>$userfile_name</strong> isimli dosya izin verilmeyen dosya türü.", "W");
			return false;
		}
	endif;

	// $large_image_name = time() . '_' . fn_MakeSeo($upload_image_name) . "." . $file_ext;
	$large_image_name = uniqid() . "." . $file_ext;

	$large_image_location = $file_upload_dir . $large_image_name;

	if (move_uploaded_file($userfile_tmp, $large_image_location)) {

		return 	$large_image_name;
	}

	return false;
}

//
/// İnclude page set
//
// return @string : right inc , target_file
//
/// İnclude page set
//
// return @string : right inc , target_file
function fn_target_inc_set()
{
	global $target, $s_tema;

	$def_target = 'home';
	$r_p = array();
	$area = (AREA == 'A') ? 'admin' : 'customer';

	// 1- THEME_ADDONS
	// 2- ADDONS
	// 3- THEME TARGET

	// TARGET
	if (AREA != 'A') {


		if (is_file(TARGET_DIR . DS . "$target" . ".php")) {

			return $r_p['target'] = TARGET_DIR . DS . "$target" . ".php";
		}

		if (is_file($s_tema . "/target/$target.php")) {
			return $r_p['target'] = $s_tema . "/target/$target.php";
		}
	}


	// ADDONS KONTROL
	if (is_file(ADDONS_DIR . "/" . $target . "/$area/" . $target . ".php")) {
		$r_p['target'] = ADDONS_DIR . "/" . $target . "/$area/" . $target . ".php";
		return $r_p['target'];
	}

	if (is_file(TARGET_DIR . "/$target.php")) {
		$r_p['target'] = TARGET_DIR . "/$target.php";
		return $r_p['target'];
	}


	$r_p['target'] = TARGET_DIR . "/$def_target.php";
	return $r_p['target'];
}


function print_e($ar)
{
	pe($ar);
	exit;
}
function ped($ar)
{
	pe($ar);
	die;
}
function pe($ar)
{

	echo '<pre>';
	print_r($ar);
	echo '</pre>';
}

//
// Restore original variable content (unstripped)
// Parameters should be the variables names
// E.g. fn_trusted_vars("product_data","big_text","etcetc")
function fn_trusted_vars()
{
	$args = func_get_args();
	if (sizeof($args) > 0) {
		foreach ($args as $k => $v) {
			/*if (isset($_GET[$v])) {
				$GLOBALS[$v] = (QUOTES_ENABLED == 1) ? $_GET[$v] : fn_add_slashes($_GET[$v]);
			}*/
			if (isset($_POST[$v])) {
				$GLOBALS[$v] = (defined('QUOTES_ENABLED')) ? $_POST[$v] : fn_add_slashes($_POST[$v]);
			}
		}
	}
	return true;
}




function fn_redirect_submit($location, $data = array(), $no_delay = true)
{
	global $config, $target, $id;

	$form_submit_options = $config['form_submit_option'];


	switch ($form_submit_options) {
		case 'Kaydet ve Kal':
			$location = "?do=$target/update&id=$id";
			break;
		case 'Kaydet ve Listele':
			$location = "?do=$target/manage&id=$id";
			break;

		case 'Kaydet ve Yeni Ekle':
			$location = "?do=$target/add";
			break;

		default:
			# code...
			break;
	}

	if (AJAX_REQUEST) {
		send_json($data);
	}

	if (!headers_sent()) {

		header('Location: ' . $location);
		exit();
	} else {
		$delay = ($no_delay == true) ? 0 : 5;
		if ($no_delay == false) {
			echo '<hr width="100%" />';
			echo 'İşleminiz gerçekleşti/gerçekleşiyor.' . '&nbsp;';
			echo '<a href="' . $location . '">devam</a>';
			echo '<br /><br />';
		}
		echo "<meta http-equiv=\"Refresh\" content=\"$delay;URL=$location\" />";
	}

	fn_flush();
	exit();
}
function fn_redirect($location)
{
	global $data, $no_delay;


	if (defined(AJAX_REQUEST)) {

		send_json($data);
	}

	if (!headers_sent()) {

		header('Location: ' . $location);
		exit();
	} else {
		$delay = ($no_delay == true) ? 0 : 5;
		if ($no_delay == false) {
			echo '<hr width="100%" />';
			echo 'İşleminiz gerçekleşti/gerçekleşiyor.' . '&nbsp;';
			echo '<a href="' . $location . '">devam</a>';
			echo '<br /><br />';
		}
		echo "<meta http-equiv=\"Refresh\" content=\"$delay;URL=$location\" />";
	}

	fn_flush();
	exit();
}

function fn_flush()
{
	if (function_exists('ob_flush')) {
		@ob_flush();
	}

	flush();
}

//
// UNIX-TIME bugünün tarihine çevir
//
function unix_to_date($unix_time, $tip = 3)
{
	if (empty($unix_time)) return '-';
	switch ($tip) {
		case 1:
			$tarih = date('d/m/Y', $unix_time);
			break;
		case 2:
			$tarih = date('d/m/Y H:i', $unix_time);
			break;
		case 3:
			$tarih = date('d/m/Y H:i:s', $unix_time);
			break;
	}

	return $tarih;
}

function date_to_unix($tarih)
{

	//$tarih = "25.05.2005 13:54:00";

	//Verilen Tarih Gün, Ay, Yýl, Saat, Dakika, Saniye olarak parçalara ayrýlýyor
	// @list($gun,$ay,$yil,$saat,$dakika,$saniye) = preg_replace("[/\ \:]",$tarih);

	// return $unix_time;

	// 24 Subat 2021 @worqzone
	// Daha yeni bir yontem. Ustteki cok eskimis.

	$sonuc = date_parse_from_format('d/m/Y H:i:s', $tarih);
	$unix_time = mktime($sonuc['hour'], $sonuc['minute'], $sonuc['second'], $sonuc['month'], $sonuc['day'], $sonuc['year']);
	return $unix_time;
}


// File delete
function fn_delete_file($file_path)
{

	if (empty($file_path)) return false;

	unlink(@$file_path);

	return true;
}

// Stripslashes wrapper. Returns variable with slashes stripped if magic
// quotes are enabled. Return var as is else.
function fn_stripslashes($var)
{
	if (is_array($var)) {
		$var = array_map('fn_stripslashes', $var);
		return $var;
	}

	return (strpos($var, '\\\'') !== false || strpos($var, '\\\\') !== false || strpos($var, '\\"') !== false) ? stripslashes($var) : $var;
}


//
/// dir list
//

function directory_list($directory_base_path, $filter_dir = false, $filter_files = false, $exclude = ".|..|.DS_Store|.svn", $recursive = true)
{
	$directory_base_path = rtrim($directory_base_path, "/") . "/";

	if (!is_dir($directory_base_path)) {
		echo ("File at: $directory_base_path is not a directory.");
		return false;
	}

	$result_list = array();
	$exclude_array = explode("|", $exclude);

	if (!$folder_handle = opendir($directory_base_path)) {
		echo ("Could not open directory at: $directory_base_path");
		return false;
	} else {
		while (false !== ($filename = readdir($folder_handle))) {
			if (!in_array($filename, $exclude_array)) {
				if (@is_dir($directory_base_path . $filename . "/")) {
					if ($recursive && strcmp($filename, ".") != 0 && strcmp($filename, "..") != 0) { // prevent infinite recursion
						error_log($directory_base_path . $filename . "/");
						$result_list[$filename] = directory_list("$directory_base_path$filename/", $filter_dir, $filter_files, $exclude, $recursive);
					} elseif (!$filter_dir) {
						$result_list[] = $filename;
					}
				} elseif (!$filter_files) {
					$result_list[] = $filename;
				}
			}
		}
		closedir($folder_handle);
		return $result_list;
	}
}


//
/// get Settings
// general.title
// return $string

function fn_get_settings($a)
{
	global $settings;

	$b = explode(".", $a);

	return @$settings["$b[0]"]["$b[1]"];
}

//
/// Notice
//
function fn_set_notice($mesaj, $tip = "S")
{

	if ($tip == 'E') $_SESSION['message']['E'][] = $mesaj;
	if ($tip == 'W') $_SESSION['message']['W'][] = $mesaj;
	if ($tip == 'S') $_SESSION['message']['S'][] = $mesaj;
}


# LOCATION

//
/// Gel il
//
function get_il($id)
{
	global $db, $db_tables;

	if (!empty($id)) {

		return $db->get_row("SELECT * FROM $db_tables[il] WHERE IL_ID='$id'");
	}
	return $db->get_results("SELECT * FROM $db_tables[il] order by  IL_ADI asc");
}
function get_ilce($il_id, $ilce_id = 0)
{
	global $db, $db_tables;

	if (!empty($ilce_id)) {

		return $db->get_row("SELECT * FROM $db_tables[ilce] WHERE ILCE_ID='$ilce_id'");
	}

	return $db->get_results("SELECT * FROM $db_tables[ilce] WHERE IL_ID='$il_id' order by  ILCE_ADI asc");
}

// Chech mail
function fn_check_mail($mail)
{

	if (!preg_match('[^\w+@\w+\.\w[\w\.]+$]', $mail))
		return false;
	else
		return true;
}


//
/// Ortaklık başlar
// 04.08.2012 Evrim.